<script lang="ts" setup>
import CommonProperty from './CommonProperty.vue';
import User from './User.vue';

defineOptions({
  name: 'PropertyDialog',
});

defineProps({
  nodeData: {
    type: Object,
    default: () => ({}),
  },
  lf: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(['setPropertiesFinish']);

const handleClose = () => {
  emit('setPropertiesFinish');
};
</script>
<template>
  <div class="property-dialog">
    <User
      v-if="nodeData?.type === 'user'"
      :node-data="nodeData"
      :lf="lf"
      @on-close="handleClose"
    />
    <CommonProperty
      v-else
      :node-data="nodeData"
      :lf="lf"
      @on-close="handleClose"
    />
  </div>
</template>
<style>
.property-dialog {
  padding: 20px;
}
</style>
