import type LogicFlow from '@logicflow/core';

import { computed, createApp, defineComponent, ref } from 'vue';

/**
 * 现代化卡片节点 - Vue组件版本
 */
export function registerModernCardNode(lf: LogicFlow) {
  // 定义Vue组件
  const ModernCardComponent = defineComponent({
    name: 'ModernCardNode',
    props: {
      properties: {
        type: Object,
        default: () => ({}),
      },
    },
    setup(props) {
      const title = ref(props.properties?.title || '现代化卡片');
      const description = ref(
        props.properties?.description || '具有渐变背景和动画效果的现代化节点',
      );
      const status = ref(props.properties?.status || 'active');
      const priority = ref(props.properties?.priority || 'medium');
      const progress = ref(props.properties?.progress || 75);
      const timeInfo = ref(props.properties?.timeInfo || '2h 30m');

      const statusClass = computed(() => `status-${status.value}`);
      const priorityText = computed(() => {
        const priorities: Record<string, string> = {
          low: '低',
          medium: '中',
          high: '高',
          urgent: '紧急',
        };
        return priorities[priority.value] || '中';
      });

      return {
        title,
        description,
        status,
        priority,
        progress,
        timeInfo,
        statusClass,
        priorityText,
      };
    },
    template: `
      <div class="modern-card-wrapper">
        <div class="modern-card-content">
          <!-- 装饰性动画圆圈 -->
          <div class="decoration-circles">
            <div class="circle circle-1"></div>
            <div class="circle circle-2"></div>
            <div class="circle circle-3"></div>
          </div>
          
          <!-- 顶部状态栏 -->
          <div class="card-header">
            <div class="status-indicator" :class="statusClass"></div>
            <div class="priority-badge">{{ priorityText }}</div>
          </div>
          
          <!-- 主体内容 -->
          <div class="card-body">
            <h3 class="card-title">{{ title }}</h3>
            <p class="card-description">{{ description }}</p>
          </div>
          
          <!-- 底部信息栏 -->
          <div class="card-footer">
            <div class="progress-section">
              <span class="progress-label">进度</span>
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: progress + '%' }"></div>
              </div>
              <span class="progress-text">{{ progress }}%</span>
            </div>
            <div class="time-info">
              <span class="time-icon">⏱</span>
              <span class="time-text">{{ timeInfo }}</span>
            </div>
          </div>
        </div>
      </div>
    `,
  });

  lf.register('modern-card', ({ HtmlNode, HtmlNodeModel }: any) => {
    class ModernCardNode extends HtmlNode {
      private app: any = null;

      // 组件销毁时清理Vue应用
      destroy() {
        if (this.app) {
          this.app.unmount();
          this.app = null;
        }
      }

      setHtml(rootEl: HTMLElement) {
        const { model } = this.props;
        const { properties } = model;

        // 清空容器
        rootEl.innerHTML = '';

        // 创建挂载点
        const mountPoint = document.createElement('div');
        mountPoint.style.cssText = `
          width: 280px;
          height: 140px;
          position: relative;
        `;
        rootEl.append(mountPoint);

        // 创建Vue应用
        this.app = createApp(ModernCardComponent, {
          properties: properties || {},
        });

        // 挂载Vue组件
        this.app.mount(mountPoint);

        // 注入样式（只添加一次）
        if (!document.querySelector('#modern-card-styles')) {
          const style = document.createElement('style');
          style.id = 'modern-card-styles';
          style.textContent = `
            .modern-card-wrapper {
              width: 100%;
              height: 100%;
              position: relative;
            }
            
            .modern-card-content {
              width: 280px;
              height: 140px;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              border-radius: 16px;
              box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
              position: relative;
              overflow: hidden;
              cursor: pointer;
              transition: all 0.3s ease;
              color: white;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              display: flex;
              flex-direction: column;
            }
            
            .modern-card-content:hover {
              transform: translateY(-2px) scale(1.02);
              box-shadow: 0 12px 40px rgba(102, 126, 234, 0.4);
            }
            
            .decoration-circles {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              overflow: hidden;
              pointer-events: none;
              z-index: 1;
            }
            
            .circle {
              position: absolute;
              border-radius: 50%;
              background: rgba(255, 255, 255, 0.1);
              animation: float 6s ease-in-out infinite;
            }
            
            .circle-1 {
              width: 30px;
              height: 30px;
              top: 20px;
              right: 30px;
              animation-delay: 0s;
            }
            
            .circle-2 {
              width: 20px;
              height: 20px;
              top: 60px;
              right: 60px;
              animation-delay: 2s;
            }
            
            .circle-3 {
              width: 15px;
              height: 15px;
              top: 100px;
              right: 20px;
              animation-delay: 4s;
            }
            
            @keyframes float {
              0%, 100% { transform: translateY(0px); opacity: 0.7; }
              50% { transform: translateY(-10px); opacity: 1; }
            }
            
            .card-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 12px 16px;
              border-bottom: 1px solid rgba(255, 255, 255, 0.1);
              position: relative;
              z-index: 2;
            }
            
            .status-indicator {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              position: relative;
            }
            
            .status-indicator.status-active {
              background: #4ade80;
              box-shadow: 0 0 6px #4ade80;
            }
            
            .status-indicator.status-warning {
              background: #facc15;
              box-shadow: 0 0 6px #facc15;
            }
            
            .status-indicator.status-error {
              background: #ef4444;
              box-shadow: 0 0 6px #ef4444;
            }
            
            .status-indicator::after {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              border-radius: 50%;
              animation: pulse 2s infinite;
            }
            
            .status-active::after {
              background: #4ade80;
            }
            
            .status-warning::after {
              background: #facc15;
            }
            
            .status-error::after {
              background: #ef4444;
            }
            
            @keyframes pulse {
              0% { transform: scale(1); opacity: 1; }
              50% { transform: scale(1.5); opacity: 0.5; }
              100% { transform: scale(2); opacity: 0; }
            }
            
            .priority-badge {
              background: rgba(255, 255, 255, 0.15);
              padding: 2px 8px;
              border-radius: 12px;
              font-size: 10px;
              font-weight: 500;
              backdrop-filter: blur(4px);
            }
            
            .card-body {
              padding: 16px;
              flex: 1;
              position: relative;
              z-index: 2;
            }
            
            .card-title {
              margin: 0 0 8px 0;
              font-size: 14px;
              font-weight: 600;
              line-height: 1.2;
            }
            
            .card-description {
              margin: 0;
              font-size: 11px;
              opacity: 0.9;
              line-height: 1.4;
            }
            
            .card-footer {
              padding: 12px 16px;
              border-top: 1px solid rgba(255, 255, 255, 0.1);
              position: relative;
              z-index: 2;
            }
            
            .progress-section {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 8px;
            }
            
            .progress-label {
              font-size: 10px;
              opacity: 0.8;
              min-width: 24px;
            }
            
            .progress-bar {
              flex: 1;
              height: 4px;
              background: rgba(255, 255, 255, 0.2);
              border-radius: 2px;
              overflow: hidden;
            }
            
            .progress-fill {
              height: 100%;
              background: linear-gradient(90deg, #4ade80, #22c55e);
              border-radius: 2px;
              transition: width 0.3s ease;
            }
            
            .progress-text {
              font-size: 10px;
              opacity: 0.8;
              min-width: 30px;
              text-align: right;
            }
            
            .time-info {
              display: flex;
              align-items: center;
              gap: 4px;
              font-size: 10px;
              opacity: 0.8;
            }
            
            .time-icon {
              font-size: 12px;
            }
          `;
          document.head.append(style);
        }
      }
    }

    class ModernCardModel extends HtmlNodeModel {
      initNodeData(data: any) {
        super.initNodeData(data);
        this.width = 280;
        this.height = 140;
        this.text.editable = false;

        this.properties = {
          title: '现代化卡片',
          description: '具有渐变背景和动画效果的现代化节点',
          status: 'active', // active, warning, error
          priority: 'medium', // low, medium, high, urgent
          progress: 75,
          timeInfo: '2h 30m',
          ...this.properties,
        };
      }
    }

    return { view: ModernCardNode, model: ModernCardModel };
  });
}

/**
 * 仪表盘节点 - Vue组件版本
 */
export function registerDashboardNode(lf: LogicFlow) {
  const DashboardComponent = defineComponent({
    name: 'DashboardNode',
    props: {
      properties: {
        type: Object,
        default: () => ({}),
      },
    },
    setup(props) {
      const title = ref(props.properties?.title || '数据仪表盘');
      const subtitle = ref(props.properties?.subtitle || '实时监控面板');
      const status = ref(props.properties?.status || 'online');
      const iconColor = ref(props.properties?.iconColor || '#3b82f6');

      const stats = ref(
        props.properties?.stats || [
          {
            label: '总数',
            value: '1,234',
            color: '#3b82f6',
            trend: 'up',
            change: '+12%',
          },
          {
            label: '活跃',
            value: '856',
            color: '#10b981',
            trend: 'up',
            change: '+8%',
          },
          {
            label: '错误',
            value: '12',
            color: '#ef4444',
            trend: 'down',
            change: '-5%',
          },
        ],
      );

      const progressData = ref(
        props.properties?.progressData || [
          {
            name: 'CPU使用率',
            value: 65,
            color: 'linear-gradient(90deg, #3b82f6, #1d4ed8)',
          },
          {
            name: '内存使用',
            value: 82,
            color: 'linear-gradient(90deg, #10b981, #059669)',
          },
          {
            name: '存储空间',
            value: 45,
            color: 'linear-gradient(90deg, #f59e0b, #d97706)',
          },
        ],
      );

      const statusText = computed(() => {
        const statusMap: Record<string, string> = {
          online: '在线',
          offline: '离线',
          warning: '警告',
          error: '错误',
        };
        return statusMap[status.value] || '未知';
      });

      return {
        title,
        subtitle,
        status,
        iconColor,
        stats,
        progressData,
        statusText,
      };
    },
    template: `
      <div class="dashboard-wrapper">
        <div class="dashboard-content">
          <!-- 头部 -->
          <div class="dashboard-header">
            <div class="header-left">
              <div class="node-icon" :style="{ background: iconColor }">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
                  <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
                </svg>
              </div>
              <div class="header-info">
                <h3 class="node-title">{{ title }}</h3>
                <p class="node-subtitle">{{ subtitle }}</p>
              </div>
            </div>
            <div class="header-right">
              <div class="status-badge" :class="'status-' + status">
                {{ statusText }}
              </div>
            </div>
          </div>
          
          <!-- 统计数据 -->
          <div class="stats-section">
            <div class="stat-item" v-for="stat in stats" :key="stat.label">
              <div class="stat-value" :style="{ color: stat.color }">{{ stat.value }}</div>
              <div class="stat-label">{{ stat.label }}</div>
              <div class="stat-trend" :class="'trend-' + stat.trend">
                <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                  <path v-if="stat.trend === 'up'" d="M7 14l5-5 5 5z"/>
                  <path v-else-if="stat.trend === 'down'" d="M7 10l5 5 5-5z"/>
                  <circle v-else cx="12" cy="12" r="2"/>
                </svg>
                {{ stat.change }}
              </div>
            </div>
          </div>
          
          <!-- 进度指示器 -->
          <div class="progress-indicators">
            <div class="progress-item" v-for="progress in progressData" :key="progress.name">
              <div class="progress-info">
                <span class="progress-name">{{ progress.name }}</span>
                <span class="progress-value">{{ progress.value }}%</span>
              </div>
              <div class="progress-bar-container">
                <div class="progress-bar-bg"></div>
                <div 
                  class="progress-bar-fill" 
                  :style="{ 
                    width: progress.value + '%',
                    background: progress.color 
                  }"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    `,
  });

  lf.register('dashboard-node', ({ HtmlNode, HtmlNodeModel }: any) => {
    class DashboardNode extends HtmlNode {
      private app: any = null;

      destroy() {
        if (this.app) {
          this.app.unmount();
          this.app = null;
        }
      }

      setHtml(rootEl: HTMLElement) {
        const { model } = this.props;
        const { properties } = model;

        rootEl.innerHTML = '';

        const mountPoint = document.createElement('div');
        mountPoint.style.cssText = `
          width: 320px;
          height: 200px;
          position: relative;
        `;
        rootEl.append(mountPoint);

        this.app = createApp(DashboardComponent, {
          properties: properties || {},
        });

        this.app.mount(mountPoint);

        if (!document.querySelector('#dashboard-styles')) {
          const style = document.createElement('style');
          style.id = 'dashboard-styles';
          style.textContent = `
            .dashboard-wrapper {
              width: 100%;
              height: 100%;
              position: relative;
            }
            
            .dashboard-content {
              width: 320px;
              height: 200px;
              background: #ffffff;
              border-radius: 20px;
              box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
              border: 1px solid #f0f0f0;
              padding: 20px;
              display: flex;
              flex-direction: column;
              gap: 16px;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              transition: all 0.3s ease;
              cursor: pointer;
            }
            
            .dashboard-content:hover {
              box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
              transform: translateY(-2px);
            }
            
            .dashboard-header {
              display: flex;
              align-items: center;
              justify-content: space-between;
            }
            
            .header-left {
              display: flex;
              align-items: center;
              gap: 12px;
            }
            
            .node-icon {
              width: 48px;
              height: 48px;
              border-radius: 12px;
              display: flex;
              align-items: center;
              justify-content: center;
              box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            }
            
            .header-info {
              flex: 1;
            }
            
            .node-title {
              font-size: 16px;
              font-weight: 600;
              margin: 0 0 4px 0;
              color: #1f2937;
            }
            
            .node-subtitle {
              font-size: 12px;
              color: #6b7280;
              margin: 0;
            }
            
            .status-badge {
              padding: 4px 12px;
              border-radius: 20px;
              font-size: 12px;
              font-weight: 500;
            }
            
            .status-online {
              background: #dcfce7;
              color: #166534;
            }
            
            .status-offline {
              background: #fee2e2;
              color: #991b1b;
            }
            
            .status-warning {
              background: #fef3c7;
              color: #92400e;
            }
            
            .status-error {
              background: #fee2e2;
              color: #991b1b;
            }
            
            .stats-section {
              display: flex;
              gap: 12px;
            }
            
            .stat-item {
              flex: 1;
              text-align: center;
            }
            
            .stat-value {
              font-size: 18px;
              font-weight: 700;
              margin-bottom: 4px;
            }
            
            .stat-label {
              font-size: 11px;
              color: #6b7280;
              margin-bottom: 4px;
            }
            
            .stat-trend {
              display: flex;
              align-items: center;
              justify-content: center;
              gap: 2px;
              font-size: 10px;
              font-weight: 500;
            }
            
            .trend-up {
              color: #059669;
            }
            
            .trend-down {
              color: #dc2626;
            }
            
            .progress-indicators {
              display: flex;
              flex-direction: column;
              gap: 8px;
            }
            
            .progress-item {
              display: flex;
              flex-direction: column;
              gap: 4px;
            }
            
            .progress-info {
              display: flex;
              justify-content: space-between;
              align-items: center;
            }
            
            .progress-name {
              font-size: 11px;
              color: #374151;
              font-weight: 500;
            }
            
            .progress-value {
              font-size: 11px;
              color: #6b7280;
              font-weight: 600;
            }
            
            .progress-bar-container {
              position: relative;
              height: 6px;
              border-radius: 3px;
              overflow: hidden;
            }
            
            .progress-bar-bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background: #f3f4f6;
            }
            
            .progress-bar-fill {
              position: absolute;
              top: 0;
              left: 0;
              height: 100%;
              border-radius: 3px;
              transition: width 0.3s ease;
            }
          `;
          document.head.append(style);
        }
      }
    }

    class DashboardModel extends HtmlNodeModel {
      initNodeData(data: any) {
        super.initNodeData(data);
        this.width = 320;
        this.height = 200;
        this.text.editable = false;

        this.properties = {
          title: '数据仪表盘',
          subtitle: '实时监控面板',
          status: 'online',
          iconColor: '#3b82f6',
          ...this.properties,
        };
      }
    }

    return { view: DashboardNode, model: DashboardModel };
  });
}

/**
 * 时间轴节点 - Vue组件版本
 */
export function registerTimelineNode(lf: LogicFlow) {
  const TimelineComponent = defineComponent({
    name: 'TimelineNode',
    props: {
      properties: {
        type: Object,
        default: () => ({}),
      },
    },
    setup(props) {
      const title = ref(props.properties?.title || '工作流时间轴');
      const subtitle = ref(props.properties?.subtitle || '跟踪流程进度');

      const items = ref(
        props.properties?.items || [
          {
            title: '申请提交',
            description: '用户提交了新的申请请求',
            time: '10:30',
            user: '张三',
            status: 'completed',
          },
          {
            title: '初审中',
            description: '正在进行初步审核',
            time: '11:15',
            user: '李四',
            status: 'active',
          },
          {
            title: '待复审',
            description: '等待最终审核确认',
            time: '--:--',
            user: '待分配',
            status: 'pending',
          },
        ],
      );

      const getStatusText = (status: string) => {
        const statusMap: Record<string, string> = {
          completed: '已完成',
          active: '进行中',
          pending: '待处理',
          cancelled: '已取消',
        };
        return statusMap[status] || '未知';
      };

      return {
        title,
        subtitle,
        items,
        getStatusText,
      };
    },
    template: `
      <div class="timeline-wrapper">
        <div class="timeline-content">
          <!-- 头部 -->
          <div class="timeline-header">
            <h3 class="timeline-title">{{ title }}</h3>
            <p class="timeline-subtitle">{{ subtitle }}</p>
          </div>
          
          <!-- 时间轴 -->
          <div class="timeline-body">
            <div class="timeline-item" v-for="(item, index) in items" :key="index">
              <div class="timeline-marker" :class="'marker-' + item.status">
                <div class="marker-dot"></div>
                <div class="marker-line" v-if="index < items.length - 1"></div>
              </div>
              <div class="timeline-content-item">
                <div class="item-header">
                  <h4 class="item-title">{{ item.title }}</h4>
                  <span class="item-time">{{ item.time }}</span>
                </div>
                <p class="item-description">{{ item.description }}</p>
                <div class="item-footer">
                  <span class="item-user">👤 {{ item.user }}</span>
                  <span class="item-status" :class="'status-' + item.status">
                    {{ getStatusText(item.status) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    `,
  });

  lf.register('timeline-node', ({ HtmlNode, HtmlNodeModel }: any) => {
    class TimelineNode extends HtmlNode {
      private app: any = null;

      destroy() {
        if (this.app) {
          this.app.unmount();
          this.app = null;
        }
      }

      setHtml(rootEl: HTMLElement) {
        const { model } = this.props;
        const { properties } = model;

        rootEl.innerHTML = '';

        const mountPoint = document.createElement('div');
        mountPoint.style.cssText = `
          width: 350px;
          height: 240px;
          position: relative;
        `;
        rootEl.append(mountPoint);

        this.app = createApp(TimelineComponent, {
          properties: properties || {},
        });

        this.app.mount(mountPoint);

        if (!document.querySelector('#timeline-styles')) {
          const style = document.createElement('style');
          style.id = 'timeline-styles';
          style.textContent = `
            .timeline-wrapper {
              width: 100%;
              height: 100%;
              position: relative;
            }
            
            .timeline-content {
              width: 350px;
              height: 240px;
              background: #ffffff;
              border-radius: 16px;
              box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
              border: 1px solid #e5e7eb;
              padding: 20px;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              transition: all 0.3s ease;
              cursor: pointer;
            }
            
            .timeline-content:hover {
              box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
              transform: translateY(-2px);
            }
            
            .timeline-header {
              margin-bottom: 16px;
              padding-bottom: 12px;
              border-bottom: 1px solid #f3f4f6;
            }
            
            .timeline-title {
              font-size: 16px;
              font-weight: 600;
              margin: 0 0 4px 0;
              color: #1f2937;
            }
            
            .timeline-subtitle {
              font-size: 12px;
              color: #6b7280;
              margin: 0;
            }
            
            .timeline-body {
              max-height: 160px;
              overflow-y: auto;
            }
            
            .timeline-item {
              display: flex;
              position: relative;
              margin-bottom: 16px;
            }
            
            .timeline-item:last-child {
              margin-bottom: 0;
            }
            
            .timeline-marker {
              width: 24px;
              display: flex;
              flex-direction: column;
              align-items: center;
              margin-right: 12px;
            }
            
            .marker-dot {
              width: 10px;
              height: 10px;
              border-radius: 50%;
              border: 2px solid transparent;
              position: relative;
              z-index: 2;
            }
            
            .marker-completed .marker-dot {
              background: #10b981;
              border-color: #10b981;
            }
            
            .marker-active .marker-dot {
              background: #3b82f6;
              border-color: #3b82f6;
              animation: pulse-marker 2s infinite;
            }
            
            .marker-pending .marker-dot {
              background: #d1d5db;
              border-color: #d1d5db;
            }
            
            .marker-cancelled .marker-dot {
              background: #ef4444;
              border-color: #ef4444;
            }
            
            .marker-line {
              flex: 1;
              width: 2px;
              background: #e5e7eb;
              margin-top: 4px;
            }
            
            @keyframes pulse-marker {
              0%, 100% { transform: scale(1); }
              50% { transform: scale(1.2); }
            }
            
            .timeline-content-item {
              flex: 1;
              min-width: 0;
            }
            
            .item-header {
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              margin-bottom: 4px;
            }
            
            .item-title {
              font-size: 14px;
              font-weight: 600;
              margin: 0;
              color: #1f2937;
            }
            
            .item-time {
              font-size: 11px;
              color: #6b7280;
              background: #f9fafb;
              padding: 2px 6px;
              border-radius: 6px;
            }
            
            .item-description {
              font-size: 12px;
              color: #4b5563;
              margin: 0 0 8px 0;
              line-height: 1.4;
            }
            
            .item-footer {
              display: flex;
              justify-content: space-between;
              align-items: center;
            }
            
            .item-user {
              font-size: 11px;
              color: #6b7280;
            }
            
            .item-status {
              font-size: 10px;
              font-weight: 500;
              padding: 2px 8px;
              border-radius: 10px;
            }
            
            .status-completed {
              background: #d1fae5;
              color: #065f46;
            }
            
            .status-active {
              background: #dbeafe;
              color: #1e40af;
            }
            
            .status-pending {
              background: #f3f4f6;
              color: #374151;
            }
            
            .status-cancelled {
              background: #fee2e2;
              color: #991b1b;
            }
          `;
          document.head.append(style);
        }
      }
    }

    class TimelineModel extends HtmlNodeModel {
      initNodeData(data: any) {
        super.initNodeData(data);
        this.width = 350;
        this.height = 240;
        this.text.editable = false;

        this.properties = {
          title: '工作流时间轴',
          subtitle: '跟踪流程进度',
          ...this.properties,
        };
      }
    }

    return { view: TimelineNode, model: TimelineModel };
  });
}

/**
 * 注册所有Vue组件节点
 */
export function registerAllVueComponentNodes(lf: LogicFlow) {
  registerModernCardNode(lf);
  registerDashboardNode(lf);
  registerTimelineNode(lf);
}
