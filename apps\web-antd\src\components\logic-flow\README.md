# Logic Flow 工作流组件

基于 LogicFlow 的审批工作流设计器组件，支持制药质控 LIMS 系统的工作流管理。

## 📁 文件结构

```
logic-flow/
├── README.md                           # 组件说明文档
├── index.ts                           # 组件导出入口
├── WorkflowDesigner.vue               # 主要工作流设计器组件
├── WorkflowExample.vue                # 使用示例组件
│
├── components/                        # 子组件
│   ├── WorkflowNodePanel.vue         # 节点面板
│   └── WorkflowPropertyPanel.vue     # 属性设置面板
│
├── config/                           # 配置文件
│   ├── nodes.ts                      # 节点配置和模板
│   └── condition-node.ts             # 条件节点专用配置
│
├── types/                           # 类型定义
│   └── workflow.ts                  # 工作流相关类型
│
├── utils/                          # 工具函数
│   └── validator.ts                # 工作流验证器
│
├── registerNode/                   # 节点注册（LogicFlow 相关）
│   ├── index.ts                   # 注册函数导出
│   └── BaseNodes.ts              # 基础节点注册
│
├── docs/                          # 文档
│   ├── README.md                  # 详细使用文档
│   └── condition-node-guide.md   # 条件节点使用指南
│
├── assets/                        # 静态资源
│   └── images/                    # 图片资源
│       ├── start.png
│       ├── end.png
│       └── ...
│
└── legacy/                        # 待清理的旧代码
    ├── logic-flow.vue            # 旧版本组件
    ├── LogicFlowComponents/      # 旧版本子组件
    ├── PropertySetting/          # 旧版本属性设置
    ├── registerNode/             # 旧版本节点注册
    ├── config.ts                 # 旧版本配置
    └── data.json                 # 示例数据
```

## 🚀 主要组件

### WorkflowDesigner.vue

- 主要的工作流设计器组件
- 支持编辑和查看模式
- 提供完整的工作流设计功能

### WorkflowExample.vue

- 使用示例和演示组件
- 展示组件的各种用法

## 📝 整理建议

### 需要保留的文件

- `WorkflowDesigner.vue` - 主组件
- `components/` - 现代化的子组件
- `config/nodes.ts` - 节点配置
- `types/workflow.ts` - 类型定义
- `utils/validator.ts` - 验证器
- `registerNode/BaseNodes.ts` - 基础节点注册
- `docs/` - 文档

### 需要移动的文件

- `background/` → `assets/images/`
- `config.ts` → `legacy/config.ts`
- `data.json` → `legacy/data.json`

### 需要清理的文件

- `logic-flow.vue` - 旧版本，功能已被 WorkflowDesigner.vue 替代
- `LogicFlowComponents/` - 旧版本子组件
- `PropertySetting/` - 旧版本属性设置
- `registerNode/` 中除 BaseNodes.ts 外的文件

## 🔧 使用方式

```vue
<template>
  <WorkflowDesigner
    v-model:flow-data="flowData"
    :mode="mode"
    :approver-data-source="approverDataSource"
    @node-select="handleNodeSelect"
    @validate="handleValidate"
  />
</template>

<script setup>
import { WorkflowDesigner } from '@/components/logic-flow';
</script>
```
