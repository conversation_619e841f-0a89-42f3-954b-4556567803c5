<script lang="ts" setup>
import type LogicFlow from '@logicflow/core';

import { Button, TabPane, Tabs } from 'ant-design-vue';

interface NodeItem {
  type: string;
  label: string;
}

interface NodeData {
  id: string;
  x: number;
  y: number;
}

interface Props {
  nodeData: NodeData;
  lf: LogicFlow;
}

defineOptions({
  name: 'AddPanel',
});

const props = defineProps<Props>();

const emit = defineEmits<{
  addNodeFinish: [];
}>();

const nodeList: NodeItem[] = [
  {
    type: 'user',
    label: '用户',
  },
  {
    type: 'push',
    label: '推送',
  },
];

const addNode = (item: NodeItem) => {
  const { lf, nodeData } = props;
  const { id, x, y } = nodeData;
  const nextNode = lf.addNode({
    type: item.type,
    x: x + 150,
    y: y + 150,
  });
  const nextId = nextNode.id;
  lf.addEdge({ sourceNodeId: id, targetNodeId: nextId });
  emit('addNodeFinish');
};

const addTemplate = () => {
  const { lf, nodeData } = props;
  const { id, x, y } = nodeData;
  const timeNode = lf.addNode({
    type: 'download',
    x,
    y: y + 150,
  });
  const userNode = lf.addNode({
    type: 'user',
    x: x + 150,
    y: y + 150,
  });
  const pushNode = lf.addNode({
    type: 'push',
    x: x + 150,
    y: y + 300,
    properties: {},
  });
  const endNode = lf.addNode({
    type: 'end',
    x: x + 300,
    y: y + 150,
  });
  const endNode2 = lf.addNode({
    type: 'end',
    x: x + 300,
    y: y + 300,
  });
  lf.addEdge({ sourceNodeId: id, targetNodeId: timeNode.id });
  lf.addEdge({ sourceNodeId: timeNode.id, targetNodeId: userNode.id });
  lf.addEdge({
    sourceNodeId: userNode.id,
    targetNodeId: endNode.id,
    endPoint: { x: x + 280, y: y + 150 },
    text: {
      value: 'Y',
      x: x + 230,
      y: y + 140,
    },
  });
  lf.addEdge({
    sourceNodeId: userNode.id,
    targetNodeId: pushNode.id,
    text: {
      value: 'N',
      x: x + 160,
      y: y + 230,
    },
  });
  lf.addEdge({
    sourceNodeId: pushNode.id,
    targetNodeId: endNode2.id,
    endPoint: { x: x + 280, y: y + 300 },
  });
  emit('addNodeFinish');
};
</script>
<template>
  <Tabs tab-position="left">
    <TabPane key="actions" tab="添加动作">
      <div v-for="item in nodeList" :key="item.type">
        <Button
          class="add-node-btn"
          type="primary"
          size="small"
          @click="addNode(item)"
        >
          {{ item.label }}
        </Button>
      </div>
    </TabPane>
    <TabPane key="groups" tab="添加组">
      <Button
        class="add-node-btn"
        type="primary"
        size="small"
        @click="addTemplate"
      >
        模板
      </Button>
    </TabPane>
  </Tabs>
</template>
<style scoped>
.add-node-btn {
  margin-right: 20px;
  margin-bottom: 10px;
}
</style>
