﻿using Ardalis.Result;
using Witlab.Platform.Core.Platform;
using Witlab.Platform.Core.Platform.Interfaces;
using Witlab.Platform.Core.Platform.ValueObjects;
using Witlab.Platform.Infrastructure.Auth.Interfaces;
using Witlab.Platform.Infrastructure.Auth.Models;

namespace Witlab.Platform.Infrastructure.Auth;

/// <summary>
/// 认证服务实现
/// </summary>
public class AuthService : IAuthService
{
  private readonly IUserService _userService;
  private readonly IPermissionService _permissionService;
  private readonly IJwtService _jwtService;
  private readonly IRefreshTokenService _refreshTokenService;
  private readonly IUserSessionService _userSessionService;
  private readonly JwtSettings _jwtSettings;
  private readonly SessionSettings _sessionSettings;

  public AuthService(
      IUserService userService,
      IPermissionService permissionService,
      IJwtService jwtService,
      IRefreshTokenService refreshTokenService,
      IUserSessionService userSessionService,
      IOptions<JwtSettings> jwtSettings,
      IOptions<SessionSettings> sessionSettings)
  {
    _userService = userService;
    _permissionService = permissionService;
    _jwtService = jwtService;
    _refreshTokenService = refreshTokenService;
    _userSessionService = userSessionService;
    _jwtSettings = jwtSettings.Value;
    _sessionSettings = sessionSettings.Value;
  }

  /// <inheritdoc />
  public async Task<Result<AuthResponse>> LoginAsync(string userName, string password, SessionInfo? sessionInfo = null, LoginSource loginSource = LoginSource.Web)
  {
    // 验证用户名和密码
    var userResult = await _userService.ValidateUserAsync(userName, password);
    if (!userResult.IsSuccess)
    {
      return Result.Error("用户名或密码错误");
    }

    var user = userResult.Value;

    // 获取用户角色
    var rolesResult = await _userService.GetUserRolesAsync(user.Id);
    if (!rolesResult.IsSuccess)
    {
      return Result.Error("获取用户角色失败");
    }

    var roles = rolesResult.Value;

    // 生成访问令牌
    var accessToken = _jwtService.GenerateAccessToken(user, roles);

    // 生成刷新令牌
    var refreshToken = _jwtService.GenerateRefreshToken();
    var refreshTokenExpiry = DateTime.UtcNow.AddDays(_jwtSettings.RefreshTokenExpirationDays);

    // 保存刷新令牌
    await _refreshTokenService.CreateRefreshTokenAsync(user.Id, refreshToken, refreshTokenExpiry);

    // 创建用户会话（如果启用会话跟踪）
    if (_sessionSettings.EnableSessionTracking && sessionInfo != null)
    {
      var tokenId = _jwtService.GetJtiFromToken(accessToken);
      if (!string.IsNullOrEmpty(tokenId))
      {
        var tokenExpiresAt = DateTime.UtcNow.AddMinutes(_jwtSettings.AccessTokenExpirationMinutes);
        var sessionResult = await _userSessionService.CreateSessionAsync(
          user.Id,
          user.UserName,
          tokenId,
          tokenExpiresAt,
          sessionInfo,
          loginSource);

        if (!sessionResult.IsSuccess)
        {
          // 记录日志但不影响登录流程
          // 可以考虑添加日志记录
        }
      }
    }

    // 返回认证响应
    return Result.Success(new AuthResponse
    {
      AccessToken = accessToken,
      RefreshToken = refreshToken,
      ExpiresIn = _jwtSettings.AccessTokenExpirationMinutes * 60,
      UserId = user.Id,
      UserName = user.UserName
    });
  }

  /// <inheritdoc />
  public async Task<Result> LogoutAsync(Guid userId, string accessToken, string refreshToken)
  {
    // 终止用户会话（如果启用会话跟踪）
    if (_sessionSettings.EnableSessionTracking)
    {
      var tokenId = _jwtService.GetJtiFromToken(accessToken);
      if (!string.IsNullOrEmpty(tokenId))
      {
        var sessionResult = await _userSessionService.TerminateSessionAsync(tokenId);
        if (!sessionResult.IsSuccess)
        {
          // 记录日志但不影响注销流程
          // 可以考虑添加日志记录
        }
      }
    }

    // 将访问令牌加入黑名单
    var blacklistResult = await _jwtService.AddToBlacklistAsync(accessToken);
    if (!blacklistResult)
    {
      return Result.Error("注销失败：无法将访问令牌加入黑名单");
    }

    // 撤销刷新令牌
    var revokeResult = await _refreshTokenService.RevokeRefreshTokenAsync(refreshToken);
    if (!revokeResult.IsSuccess)
    {
      return Result.Error("注销失败：无法撤销刷新令牌");
    }

    return Result.Success();
  }

  /// <inheritdoc />
  public async Task<Result<AuthResponse>> RefreshTokenAsync(string accessToken, string refreshToken, string? deptCode, string? roleCode = null)
  {
    try
    {
      Guard.Against.NullOrEmpty(accessToken, nameof(accessToken), "访问令牌不能为空");
      Guard.Against.NullOrEmpty(refreshToken, nameof(refreshToken), "刷新令牌不能为空");

      // 验证访问令牌是否在黑名单中
      var isBlacklisted = await _jwtService.IsInBlacklistAsync(accessToken);
      if (isBlacklisted)
      {
        return Result.Error("访问令牌已被撤销，请重新登录");
      }

      // 从访问令牌中获取用户ID（即使Token过期也尝试获取）
      var userId = _jwtService.GetUserIdFromToken(accessToken);
      if (userId == null)
      {
        return Result.Error("无效的访问令牌格式，请重新登录");
      }

      // 验证刷新令牌
      var storedTokenResult = await _refreshTokenService.GetRefreshTokenAsync(refreshToken);
      if (!storedTokenResult.IsSuccess || storedTokenResult.Value == null)
      {
        return Result.Error("刷新令牌不存在或已过期，请重新登录");
      }
      var storedToken = storedTokenResult.Value;
      if (!storedToken.IsActive)
      {
        var reason = storedToken.IsExpired ? "已过期" :
                   storedToken.IsUsed ? "已被使用" :
                   storedToken.IsRevoked ? "已被撤销" : "无效";
        return Result.Error($"刷新令牌{reason}，请重新登录");
      }

      if (storedToken.UserId != userId)
      {
        return Result.Error("刷新令牌与访问令牌不匹配，请重新登录");
      }

      // 获取用户信息
      var userResult = await _userService.GetUserAsync(userId.Value);
      if (!userResult.IsSuccess)
      {
        return Result.Error("用户不存在或已被禁用，请重新登录");
      }

      var user = userResult.Value;

      // 获取用户角色
      var rolesResult = await _userService.GetUserRolesAsync(user.Id);
      if (!rolesResult.IsSuccess)
      {
        return Result.Error("获取用户角色失败，请重新登录");
      }

      var roles = rolesResult.Value;

      // 将旧的刷新令牌标记为已使用
      var useTokenResult = await _refreshTokenService.UseRefreshTokenAsync(refreshToken);
      if (!useTokenResult.IsSuccess)
      {
        return Result.Error("刷新令牌使用失败，请重新登录");
      }

      var currentDeptCode = deptCode ?? _jwtService.GetDeptCodeFromToken(accessToken);
      var currentRoleCode = roleCode ?? _jwtService.GetRoleCodeFromToken(accessToken);

      // 生成新的访问令牌
      var newAccessToken = _jwtService.GenerateAccessToken(user, roles, currentDeptCode, currentRoleCode);

      // 生成新的刷新令牌
      var newRefreshToken = _jwtService.GenerateRefreshToken();
      var refreshTokenExpiry = DateTime.UtcNow.AddDays(_jwtSettings.RefreshTokenExpirationDays);

      // 保存新的刷新令牌
      try
      {
        await _refreshTokenService.CreateRefreshTokenAsync(user.Id, newRefreshToken, refreshTokenExpiry);
      }
      catch (Exception)
      {
        return Result.Error("创建新刷新令牌失败，请重新登录");
      }

      // 返回新的认证响应
      return Result.Success(new AuthResponse
      {
        AccessToken = newAccessToken,
        RefreshToken = newRefreshToken,
        ExpiresIn = _jwtSettings.AccessTokenExpirationMinutes * 60,
        UserId = user.Id,
        UserName = user.UserName
      });
    }
    catch (Exception)
    {
      return Result.Error("令牌刷新过程中发生错误，请重新登录");
    }
  }

  /// <inheritdoc />
  public async Task<Result<UserInfo>> GetUserInfoAsync(Guid userId)
  {
    // 获取用户信息
    var userResult = await _userService.GetUserAsync(userId);
    if (!userResult.IsSuccess)
    {
      return Result.Error("用户不存在");
    }

    var user = userResult.Value;

    // 获取用户角色
    var rolesResult = await _userService.GetUserRolesAsync(userId);
    if (!rolesResult.IsSuccess)
    {
      return Result.Error("获取用户角色失败");
    }

    var roles = rolesResult.Value;

    // 获取用户权限
    var permissionsResult = await _permissionService.GetUserPermissionsAsync(userId);
    if (!permissionsResult.IsSuccess)
    {
      return Result.Error("获取用户权限失败");
    }

    var permissions = permissionsResult.Value;

    // 构建用户信息
    var userInfo = new UserInfo
    {
      Id = user.Id,
      UserName = user.UserName,
      FullName = user.FullName,
      Email = user.Email,
      Phone = user.Phone,
      Icon = user.Icon,
      Roles = roles.Select(r => r.RoleCode).ToList(),
      Permissions = permissions.Select(p => p.Code).ToList()
    };

    return Result.Success(userInfo);
  }
}
