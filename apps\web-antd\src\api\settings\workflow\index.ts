import type { WitLabStep } from '#/components/logic-flow/types/workflow';

import { callServer, getDataSetNoPage } from '#/api/core';

export async function getWorkflows() {
  return getDataSetNoPage(
    'GeneralWorkFlowManager.WORKFLOWTEMPLATES_GD_NEW',
    [],
  );
}

export async function updateWorkflowStep(updateWorkflowStep: WitLabStep) {
  const {
    ORIGREC,
    SORTER,
    STEPSTATUS,
    STEPDISPSTATUS,
    STEPNAME,
    SIGNATURETYPE,
    COMMENTNAME,
    MENUS,
    PERMISSIONS,
  } = updateWorkflowStep;
  const fields: Record<string, any> = {
    SORTER,
    STEPSTATUS,
    STEP<PERSON>SPSTATUS,
    STEPNAME,
    SIGNATURETYPE,
    COMMENTNAME,
    MENUS: MENUS?.join(','),
    PERMISSIONS: PERMISSIONS?.join(','),
  };
  for (const field of Object.keys(fields)) {
    await callServer('Common.Update', [
      'GENERAL_WORKFLOW_STEPS',
      field,
      fields[field],
      ORIGREC,
    ]);
  }
}

export async function getWorkflowPermissionsByMenu(menuName: string) {
  const permissionsStr = await callServer(
    'GeneralWorkFlowManager.GET_WORKFLOW_PERMISSIONS_BY_MENU',
    [menuName],
  );
  return permissionsStr.split(',') || [];
}

export async function getWorkflowByMenu(menuName: string): Promise<string[]> {
  return await callServer('GeneralWorkFlowManager.GET_WORKFLOW_BY_MENU', [
    menuName,
  ]);
}
