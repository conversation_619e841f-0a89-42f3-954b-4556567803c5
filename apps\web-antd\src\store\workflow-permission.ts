import { computed, ref } from 'vue';

import { acceptHMRUpdate, defineStore } from 'pinia';

interface WorkflowPermissionConfig {
  /**
   * 是否在页面加载时强制刷新权限
   */
  refreshOnPageLoad: boolean;
}

/**
 * @zh_CN 工作流权限管理
 */
export const useWorkflowPermissionStore = defineStore(
  'workflow-permission',
  () => {
    // State
    const menuPermissions = ref<Record<string, string[]>>({});
    const loading = ref<Record<string, boolean>>({});
    const cacheTimestamp = ref<Record<string, number>>({});
    const config = ref<WorkflowPermissionConfig>({
      refreshOnPageLoad: true,
    });

    // 新增：当前工作流编码和步骤编码
    const workflowCode = ref<string | undefined>(undefined);
    const stepCode = ref<string | undefined>(undefined);

    // Getters
    const getMenuPermissions = computed(() => {
      return (menuName: string): string[] => {
        return menuPermissions.value[menuName] || [];
      };
    });

    const getLoading = computed(() => {
      return (menuName: string): boolean => {
        return loading.value[menuName] || false;
      };
    });

    const isCacheValid = computed(() => {
      return (menuName: string, maxAge = 5 * 60 * 1000): boolean => {
        const timestamp = cacheTimestamp.value[menuName];
        if (!timestamp) return false;
        return Date.now() - timestamp < maxAge;
      };
    });

    const hasPermission = computed(() => {
      return (menuName: string, permission: string): boolean => {
        const permissions = menuPermissions.value[menuName] || [];
        return permissions.includes(permission);
      };
    });

    // Actions
    const setMenuPermissions = (menuName: string, permissions: string[]) => {
      menuPermissions.value[menuName] = permissions;
      cacheTimestamp.value[menuName] = Date.now();
      loading.value[menuName] = false;
    };

    const setLoading = (menuName: string, isLoading: boolean) => {
      loading.value[menuName] = isLoading;
    };

    const clearMenuPermissions = (menuName?: string) => {
      if (menuName) {
        const { [menuName]: _, ...restPermissions } = menuPermissions.value;
        const { [menuName]: __, ...restTimestamps } = cacheTimestamp.value;
        const { [menuName]: ___, ...restLoading } = loading.value;

        menuPermissions.value = restPermissions;
        cacheTimestamp.value = restTimestamps;
        loading.value = restLoading;
      } else {
        menuPermissions.value = {};
        cacheTimestamp.value = {};
        loading.value = {};
      }
    };

    const clearAll = () => {
      clearMenuPermissions();
    };

    const setConfig = (newConfig: Partial<WorkflowPermissionConfig>) => {
      config.value = { ...config.value, ...newConfig };
    };

    const setWorkflowCode = (code: string | undefined) => {
      workflowCode.value = code;
    };

    const setStepCode = (code: string | undefined) => {
      stepCode.value = code;
    };

    return {
      // State
      menuPermissions,
      loading,
      cacheTimestamp,
      config,
      workflowCode, // 新增
      stepCode, // 新增
      // Getters
      getMenuPermissions,
      getLoading,
      isCacheValid,
      hasPermission,
      // Actions
      setMenuPermissions,
      setLoading,
      clearMenuPermissions,
      clearAll,
      setConfig,
      setWorkflowCode,
      setStepCode,
    };
  },
  {
    persist: {
      pick: ['menuPermissions', 'cacheTimestamp', 'config'],
    },
  },
);

// 解决热更新问题
const hot = import.meta.hot;
if (hot) {
  hot.accept(acceptHMRUpdate(useWorkflowPermissionStore, hot));
}
