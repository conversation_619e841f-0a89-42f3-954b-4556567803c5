/**
 * 审批工作流组件类型定义
 */

// 工作流模式
export type WorkflowMode = 'edit' | 'view';

// 节点类型
export type NodeType =
  | 'approval' // 审批节点
  | 'base-rect' // 基础节点
  | 'card-node' // 卡片节点
  | 'condition' // 条件节点
  | 'dashboard-node' // 数据仪表盘
  | 'end' // 结束节点
  | 'group-node' // 分组节点
  | 'icon-node' // 图标节点
  | 'merge' // 合并节点
  | 'modern-card' // 现代化卡片
  | 'parallel' // 并行节点
  | 'process-node' // 流程节点
  | 'start' // 开始节点
  | 'status-node' // 状态节点
  | 'timeline-node'; // 时间轴节点

// 审批人类型
export interface Approver {
  id: string;
  name: string;
  email?: string;
  department?: string;
  role?: string;
}

// 条件规则
export interface ConditionRule {
  field: string; // 字段名
  operator: string; // 操作符：eq, ne, gt, lt, gte, lte, in, not_in
  value: any; // 比较值
  label?: string; // 显示标签
}

// 节点属性
export interface NodeProperties {
  // 通用属性
  name?: string;
  description?: string;

  // 审批节点属性
  approvers?: Approver[];
  approvalType?: 'all' | 'majority' | 'single'; // 审批类型：单人、全部、多数
  timeLimit?: number; // 审批时限（小时）
  autoApprove?: boolean; // 超时自动审批

  // 条件节点属性
  conditions?: ConditionRule[];

  // 并行节点属性
  branches?: string[]; // 分支名称

  // 自定义属性
  [key: string]: any;
}

// 节点数据
export interface WorkflowNode {
  id: string;
  type: NodeType;
  x: number;
  y: number;
  properties: NodeProperties;
  text?: {
    value: string;
    x: number;
    y: number;
  };
  _data?: any; // 支持WitLabStep等业务数据
  isNew?: boolean; // 是否为新添加的节点
  isUpdated?: boolean; // 是否已修改
  isDeleted?: boolean; // 是否已删除
}

// 连线数据
export interface WorkflowEdge {
  id: string;
  type: string;
  sourceNodeId: string;
  targetNodeId: string;
  startPoint: { x: number; y: number };
  endPoint: { x: number; y: number };
  properties?: {
    [key: string]: any;
    condition?: string;
    description?: string;
    label?: string;
    priority?: 'high' | 'low' | 'normal' | 'urgent';
    style?: {
      animation?: boolean;
      stroke?: string;
      strokeDasharray?: string;
      strokeWidth?: number;
    };
    tags?: string[];
    validation?: {
      message?: string;
      required?: boolean;
    };
  };
  text?: {
    value: string;
    x: number;
    y: number;
  };
  pointsList?: Array<{ x: number; y: number }>;
  _data?: any; // 支持WitLabStepAction等业务数据
  isNew?: boolean; // 是否为新添加的边
  isUpdated?: boolean; // 是否已修改
  isDeleted?: boolean; // 是否已删除
}

// 工作流数据
export interface WorkflowData {
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
}

// 组件Props
export interface WorkflowComponentProps {
  // 工作流数据
  flowData?: WorkflowData;

  // 工作模式
  mode?: WorkflowMode;

  // 是否只读
  readonly?: boolean;

  // 画布配置
  config?: {
    background?: string;
    grid?: boolean;
    height?: number;
    keyboard?: boolean;
    miniMap?: boolean;
    width?: number;
  };

  // 节点模板
  nodeTemplates?: Array<{
    icon?: string;
    name: string;
    properties?: Partial<NodeProperties>;
    type: NodeType;
  }>;

  // 审批人数据源
  approverDataSource?: Approver[];
}

// 验证结果
export interface ValidationResult {
  valid: boolean;
  errors: Array<{
    edgeId?: string;
    message: string;
    nodeId?: string;
    type: 'error' | 'warning';
  }>;
}

// 组件方法
export interface WorkflowComponentMethods {
  // 获取工作流数据
  getFlowData(): WorkflowData;

  // 设置工作流数据
  setFlowData(data: WorkflowData): void;

  // 验证工作流
  validate(): ValidationResult;

  // 导出数据
  exportData(format?: 'json' | 'xml'): string;

  // 导入数据
  importData(data: string, format?: 'json' | 'xml'): boolean;

  // 清空画布
  clear(): void;

  // 适应画布
  fitView(): void;

  // 缩放
  zoom(ratio: number): void;

  // 撤销
  undo(): void;

  // 重做
  redo(): void;

  // 获取选中元素
  getSelectedElements(): { edges: WorkflowEdge[]; nodes: WorkflowNode[] };

  // 删除选中元素
  deleteSelectedElements(): void;
}

// 节点配置
export interface NodeConfig {
  type: NodeType;
  name: string;
  icon: string;
  color: string;
  description: string;
  defaultProperties: Partial<NodeProperties>;
  allowedConnections?: {
    source?: NodeType[];
    target?: NodeType[];
  };
}

// 工作流模板
export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  data: WorkflowData;
  preview?: string;
}

// 组件Events
export interface WorkflowComponentEvents {
  // 数据变更
  'update:flowData': (data: WorkflowData) => void;

  // 节点选择
  'node-select': (node: null | WorkflowNode) => void;

  // 连线选择
  'edge-select': (edge: null | WorkflowEdge) => void;

  // 节点添加
  'node-add': (node: WorkflowNode) => void;

  // 节点删除
  'node-delete': (nodeId: string) => void;

  // 节点更新
  'node-update': (node: WorkflowNode) => void;

  // 连线添加
  'edge-add': (edge: WorkflowEdge) => void;

  // 连线删除
  'edge-delete': (edgeId: string) => void;

  // 验证结果
  validate: (result: ValidationResult) => void;

  // 保存事件
  save: (data: {
    deletedEdges: WorkflowEdge[];
    deletedNodes: WorkflowNode[];
    newEdges: WorkflowEdge[];
    newNodes: WorkflowNode[];
    updatedEdges: WorkflowEdge[];
    updatedNodes: WorkflowNode[];
  }) => void;
}

export interface WitLabStep {
  AUTHORIZATION_GROUP_O: number;
  COMMENTNAME: string;
  CONSOLEITEM: string;
  DESTINATIONTYPE: string;
  END_STEP: string;
  ESIG_AGREEMENT: string;
  ESIG_AGREEMENT_TEXT: string;
  ESIG_COMMENT: string;
  ESIG_COMMENT_REQUIRED: string;
  ESIG_PASSWORD: string;
  ESIG_WITNESS: string;
  GOTOSTEPS: string;
  GROUPNAME: string;
  ISCNTSIGN: string;
  JOBDESCRIPTION: string;
  NUMBEROOFDAYS: number;
  ORIGREC: number;
  ORIGSTS: string;
  ROLES_WF: string;
  SIGNATURETYPE: string;
  SORTER: number;
  STEPCODE: string;
  STEPDISPSTATUS: string;
  STEPNAME: string;
  STEPSTATUS: string;
  TREEAUTH: string;
  USRNAM: string;
  WORKFLOWCODE: string;
  // 扩展字段 - 用于工作流设计器
  MENUS?: string[];
  PERMISSIONS?: string[];
}

export interface WitLabStepAction {
  ORIGREC: number;
  ORIGSTS: string;
  WORKFLOWCODE: string;
  STEPCODE: string;
  DISPOSITIONNAME: string;
  DISPOSITIONCODE: string;
  TOSTEPCODE: string;
  INVESTIGATIONCODE: number;
  SORTER: number;
  DISPFLAG: string;
  ISREJECT: string;
  REQUIREREASON: string;
  ISAUDITUSER: null | string;
  ISMULTIAUDIT: null | string;
  EXECUTEACTION: string;
  STEPDETCAT: null | string;
}
