import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    path: '/demos',
    name: 'Demos',
    meta: {
      icon: 'ic:baseline-view-in-ar',
      order: 1000,
      title: $t('demos.title'),
    },
    children: [
      {
        path: 'workflow',
        name: 'Workflow',
        component: () =>
          import('#/views/settings/workflow/workflow-manager.vue'),
        meta: {
          title: $t('workflow.title'),
          icon: 'flow',
        },
      },
      // {
      //   path: 'workflow-editor',
      //   name: 'WorkflowEditor',
      //   component: () => import('#/views/workflow/workflow-editor.vue'),
      //   meta: {
      //     title: $t('workflow.title'),
      //     icon: 'flow',
      //   },
      // },
    ],
  },
];

export default routes;
