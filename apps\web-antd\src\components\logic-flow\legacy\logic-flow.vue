<script lang="ts" setup>
import type LogicFlow from '@logicflow/core';

import { onMounted, ref } from 'vue';

import LogicFlowCore from '@logicflow/core';
// const LogicFlow = window.LogicFlow
import { Menu, MiniMap, Snapshot } from '@logicflow/extension';
import { Drawer, message, Modal } from 'ant-design-vue';

import { nodeList } from './config';
import demoData from './data.json';
import AddPanel from './LogicFlowComponents/AddPanel.vue';
import Control from './LogicFlowComponents/Control.vue';
import DataDialog from './LogicFlowComponents/DataDialog.vue';
import NodePanel from './LogicFlowComponents/NodePanel.vue';
import PropertyDialog from './PropertySetting/PropertyDialog.vue';
import {
  registerConnect,
  registerDownload,
  registerEnd,
  registerPolyline,
  registerPush,
  registerStart,
  registerTask,
  registerUser,
} from './registerNode';

import '@logicflow/core/dist/index.css';
import '@logicflow/extension/dist/index.css';

defineOptions({
  name: 'LF',
});

// 模板引用
const container = ref<HTMLDivElement>();

// 响应式数据
const lf = ref<LogicFlow>();
const showAddPanel = ref(false);
const addPanelStyle = ref<{
  left: number | string;
  top: number | string;
}>({
  top: 0,
  left: 0,
});

const addClickNode = ref<any>(null);
const clickNode = ref<any>(null);
const dialogVisible = ref(false);
const graphData = ref<any>(null);
const dataVisible = ref(false);
const moveData = ref<any>({});

const config = {
  background: {
    backgroundColor: '#f7f9ff',
  },
  grid: {
    size: 10,
    visible: false,
  },
  keyboard: {
    enabled: true,
  },
  edgeTextDraggable: true,
  hoverOutline: false,
};
// 初始化LogicFlow
const initLf = () => {
  // 画布配置
  if (container.value) {
    const lfInstance = new LogicFlowCore({
      ...config,
      plugins: [Menu, MiniMap, Snapshot],
      container: container.value,
    });
    lf.value = lfInstance;
    // 设置主题
    lfInstance.setTheme({
      circle: {
        stroke: '#000000',
        strokeWidth: 1,
        outlineColor: '#88f',
      },
      rect: {
        outlineColor: '#88f',
        strokeWidth: 1,
      },
      polygon: {
        strokeWidth: 1,
      },
      polyline: {
        stroke: '#000000',
        hoverStroke: '#000000',
        selectedStroke: '#000000',
        outlineColor: '#88f',
        strokeWidth: 1,
      },
      nodeText: {
        color: '#000000',
        fontSize: 12,
      },
      edgeText: {
        color: '#000000',
        fontSize: 12,
        textWidth: 100,
        background: {
          fill: '#f7f9ff',
        },
      },
    });
    registerNode();
  }
};
// 自定义节点注册
const registerNode = () => {
  if (lf.value) {
    registerStart(lf.value);
    registerUser(lf.value);
    registerEnd(lf.value);
    registerPush(lf.value, clickPlus, mouseDownPlus);
    registerDownload(lf.value);
    registerPolyline(lf.value);
    registerTask(lf.value);
    registerConnect(lf.value);
    render();
  }
};

const render = () => {
  lf.value?.render(demoData);
  setupLfEvent();
};

const setupLfEvent = () => {
  if (lf.value) {
    lf.value.on('node:click', ({ data }: any) => {
      // console.log('node:click', data);
      clickNode.value = data;
      dialogVisible.value = true;
    });
    lf.value.on('edge:click', ({ data }: any) => {
      // console.log('edge:click', data);
      clickNode.value = data;
      dialogVisible.value = true;
    });
    lf.value.on('element:click', () => {
      hideAddPanel();
    });
    // eslint-disable-next-line unused-imports/no-unused-vars
    lf.value.on('edge:add', ({ data }: any) => {
      // console.log('edge:add', data);
    });
    lf.value.on('node:mousemove', ({ data }: any) => {
      // console.log('node:mousemove');
      moveData.value = data;
    });
    lf.value.on('blank:click', () => {
      hideAddPanel();
    });
    lf.value.on('connection:not-allowed', (data: any) => {
      message.error(data.msg);
    });
    lf.value.on('node:mousemove', () => {
      // console.log('on mousemove');
    });
  }
};
const clickPlus = (e: MouseEvent, attributes: any) => {
  e.stopPropagation();
  // console.log('clickPlus', e, attributes);
  const { clientX, clientY } = e;
  // console.log(clientX, clientY);
  addPanelStyle.value.top = `${clientY - 40}px`;
  addPanelStyle.value.left = `${clientX}px`;
  showAddPanel.value = true;
  addClickNode.value = attributes;
};

// eslint-disable-next-line unused-imports/no-unused-vars
const mouseDownPlus = (e: MouseEvent, attributes: any) => {
  e.stopPropagation();
  // console.log('mouseDownPlus', e, attributes);
};

const hideAddPanel = () => {
  showAddPanel.value = false;
  addPanelStyle.value.top = 0;
  addPanelStyle.value.left = 0;
  addClickNode.value = null;
};

const closeDialog = () => {
  dialogVisible.value = false;
};

const catData = () => {
  if (lf.value) {
    graphData.value = lf.value.getGraphData();
    dataVisible.value = true;
  }
};

onMounted(() => {
  initLf();
});
</script>
<template>
  <div class="logic-flow-view">
    <!-- 辅助工具栏 -->
    <Control v-if="lf" class="demo-control" :lf="lf" @cat-data="catData" />
    <!-- 节点面板 -->
    <NodePanel v-if="lf" :lf="lf" :node-list="nodeList" />
    <!-- 画布 -->
    <div id="lf-view" ref="container"></div>
    <!-- 用户节点自定义操作面板 -->
    <AddPanel
      v-if="showAddPanel && lf"
      class="add-panel"
      :style="addPanelStyle"
      :lf="lf"
      :node-data="addClickNode"
      @add-node-finish="hideAddPanel"
    />
    <!-- 属性面板 -->
    <Drawer
      v-model:open="dialogVisible"
      title="设置节点属性"
      placement="right"
      width="500"
      @close="closeDialog"
    >
      <PropertyDialog
        v-if="dialogVisible"
        :node-data="clickNode"
        :lf="lf"
        @set-properties-finish="closeDialog"
      />
    </Drawer>
    <!-- 数据查看面板 -->
    <Modal v-model:open="dataVisible" title="数据" width="50%" :footer="null">
      <DataDialog :graph-data="graphData" />
    </Modal>
  </div>
</template>
<style>
.logic-flow-view {
  position: relative;
  height: 100vh;
}

.demo-title {
  margin: 20px;
  text-align: center;
}

.demo-control {
  position: absolute;
  top: 50px;
  right: 50px;
  z-index: 2;
}

#lf-view {
  width: calc(100% - 100px);
  height: 80%;
  margin-left: 50px;
  outline: none;
}

.time-plus {
  cursor: pointer;
}

.add-panel {
  position: absolute;
  z-index: 11;
  padding: 10px 5px;
  background-color: white;
}

@keyframes lf-animate-dash {
  to {
    stroke-dashoffset: 0;
  }
}
</style>
