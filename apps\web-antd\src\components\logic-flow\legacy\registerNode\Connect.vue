<script lang="ts" setup>
defineProps<{
  name: string;
}>();

const emit = defineEmits<{
  selectButton: [type: number];
}>();

const done = (type: number) => {
  emit('selectButton', type);
};
</script>

<template>
  <div class="connect">
    <p>{{ name }}</p>
    <p>您好，请问需要买保险吗？</p>
    <div class="button-list">
      <button @mousedown.stop="done(1)">有保险了</button>
      <button @mousedown.stop="done(2)">不需要</button>
      <button @mousedown.stop="done(3)">需要</button>
      <button @mousedown.stop="done(4)">特殊</button>
    </div>
  </div>
</template>

<style scoped>
.connect {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: 10px;
  background: #fff;
  border: 1px solid #9a9a9b;
}

.connect p {
  margin: 0;
}

.button-list {
  position: absolute;
  bottom: 10px;
}
</style>
