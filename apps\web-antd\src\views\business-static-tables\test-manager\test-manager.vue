<script setup lang="ts">
import type { TestManagerApi } from '#/api/business-static-tables/test-manager';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { Button, message, Space, TabPane, Tabs } from 'ant-design-vue';
import dayjs from 'dayjs';

import { $delTestApi, $getTestListApi } from '#/api/business-static-tables';
import { esignBegin } from '#/components/esig';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';
import { confirm } from '#/utils/utils';

import AddTestForm from './add-test-form.vue';
import Analytes from './analytes.vue';
import { useColumns, useFilterSchema } from './data';
import MethodRelatedTest from './method-related-test.vue';
import MoveCatogoryForm from './move-catogory-form.vue';
import TestPlan from './test-plan.vue';

const activeKey = ref('1');

const colums = useColumns();
const filterSchema = useFilterSchema();
const queryData = async () => {
  return $getTestListApi('All');
};

const {
  Grid: TestListGrid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  saveRowEvent,
  cancelRowEvent,
  SelectedCheckRows,
  CurrentRow,
} = useLimsGridsConfig<TestManagerApi.Test>(colums, filterSchema, queryData, {
  params: {
    limsControlId: 'dgdTestList',
    tableName: 'TESTS',
  },
});

// watch(CurrentRow, (val) => {
//   console.log(val);
// });

async function onDelete() {
  const esigData = await esignBegin({
    eventCode: 'DeleteTest',
    needPassword: true,
    needWitness: true,
    showComment: true,
    commentRequired: true,
    commentEditable: true,
    defaultComment: '你好，我是备注！',
    showEffectiveDate: true,
    showExpiryDate: true,
    defaultEffectiveDate: dayjs(),
    defaultExpiryDate: dayjs(),
    showAgreement: true,
    agreementText: `
      MIT License

  Copyright (c) 2024-present, Vben

  Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

  The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
      `,
  });
  console.log(esigData);
  const checkRecords: TestManagerApi.Test[] =
    gridApi.grid?.getCheckboxRecords();
  if (checkRecords.length === 0) {
    message.warning($t('commons.selectOne'));
    return;
  }
  await confirm($t('commons.deleteConfirm'), $t('commons.deleteConfirmTitle'));
  const delParam: TestManagerApi.DelTestParam = {
    tmnames: checkRecords.map((item) => item.TMNAME),
    testCodes: checkRecords.map((item) => item.TESTCODE),
    testcatcodes: checkRecords.map((item) => item.TESTCATCODE),
    testnames: checkRecords.map((item) => item.TESTNO),
    aTestFlags: checkRecords.map((item) => item.TESTFLAG),
  };
  const delRes = await $delTestApi(delParam);
  if (delRes) {
    if (delRes[0].length > 0) {
      message.warning(
        $t('business-static-tables.testManager.usedInTestWarn', [
          delRes[0].join(','),
        ]),
      );
    }
    if (delRes[1].length > 0) {
      message.warning(
        $t('business-static-tables.testManager.usedInSamplesWarn', [
          delRes[1].join(','),
        ]),
      );
    }
    if (delRes[2].length > 0) {
      message.warning(
        $t('business-static-tables.testManager.usedInStaticData', [
          delRes[2].join(','),
        ]),
      );
    }
  } else {
    message.success($t('commons.deleteSuccess'));
  }
  onRefresh();
}
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddTestForm,
  destroyOnClose: true,
});

const [MoveCatogoryModal, moveCatogoryModalApi] = useVbenModal({
  connectedComponent: MoveCatogoryForm,
  destroyOnClose: true,
});

function onCreate() {
  formModalApi.open();
}

function onRefresh() {
  gridApi.query();
}

async function onMoveCatogory() {
  if (SelectedCheckRows.value.length === 0) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const moveTestOrigrecs = SelectedCheckRows.value.map((item) => item.ORIGREC);
  const oldTestCatCode = SelectedCheckRows.value[0]?.TESTCATCODE ?? '';
  moveCatogoryModalApi.setData({ moveTestOrigrecs, oldTestCatCode }).open();
}
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" class="!w-[800px]" />
    <MoveCatogoryModal @success="onRefresh" />
    <div class="flex h-full flex-col">
      <div class="h-2/3 w-full">
        <TestListGrid>
          <template #toolbar-actions>
            <Space :size="[4, 0]" wrap>
              <Button type="primary" @click="onCreate">
                {{ $t('ui.actionTitle.create') }}
              </Button>
              <Button type="primary" danger @click="onDelete">
                {{ $t('ui.actionTitle.delete') }}
              </Button>
              <Button type="default" @click="onMoveCatogory">
                {{ $t('business-static-tables.testManager.moveCategory') }}
              </Button>
            </Space>
          </template>
          <template #action="{ row }">
            <template v-if="hasEditStatus(row)">
              <Button type="link" @click="saveRowEvent(row)">
                {{ $t('commons.save') }}
              </Button>
              <Button type="link" @click="cancelRowEvent(row)">
                {{ $t('commons.cancel') }}
              </Button>
            </template>
            <template v-else>
              <Button type="link" @click="editRowEvent(row)">
                {{ $t('commons.edit') }}
              </Button>
            </template>
          </template>
        </TestListGrid>
      </div>
      <div class="flex-1 bg-white px-5">
        <Tabs v-model:active-key="activeKey" class="h-full">
          <TabPane key="1" tab="分析项列表">
            <Analytes :current-test-row="CurrentRow" />
          </TabPane>

          <TabPane key="2" tab="测试方法">
            <MethodRelatedTest :current-test-row="CurrentRow" />
          </TabPane>

          <TabPane key="3" tab="测试计划">
            <TestPlan :current-test-row="CurrentRow" />
          </TabPane>

          <TabPane key="4" tab="相关质量标准" class="flex flex-1 flex-col" />
        </Tabs>
      </div>
    </div>
  </Page>
</template>

<style>
.ant-tabs-content {
  height: 100%;
}
</style>
