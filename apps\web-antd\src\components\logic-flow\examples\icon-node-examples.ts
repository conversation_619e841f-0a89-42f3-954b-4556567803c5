/**
 * Icon Node 示例配置
 * 用于测试和演示各种类型的图标节点
 */

import type { WorkflowData } from '../types/workflow';

/**
 * 预定义图标示例
 */
export const presetIconExamples: WorkflowData = {
  nodes: [
    {
      id: 'preset-1',
      type: 'icon-node',
      x: 150,
      y: 150,
      properties: {
        title: '用户管理',
        description: '管理系统用户',
        iconType: 'preset',
        iconPreset: 'user',
        iconColor: '#1890ff',
        iconSize: 14,
      },
    },
    {
      id: 'preset-2',
      type: 'icon-node',
      x: 350,
      y: 150,
      properties: {
        title: '文件上传',
        description: '上传业务文件',
        iconType: 'preset',
        iconPreset: 'upload',
        iconColor: '#52c41a',
        iconBackground: '#f6ffed',
        iconBorder: '#b7eb8f',
        iconSize: 16,
      },
    },
    {
      id: 'preset-3',
      type: 'icon-node',
      x: 550,
      y: 150,
      properties: {
        title: '数据库查询',
        description: '查询业务数据',
        iconType: 'preset',
        iconPreset: 'database',
        iconColor: '#722ed1',
        iconBackground: '#f9f0ff',
        iconBorder: '#d3adf7',
        iconSize: 12,
      },
    },
    {
      id: 'preset-4',
      type: 'icon-node',
      x: 150,
      y: 250,
      properties: {
        title: '系统设置',
        description: '配置系统参数',
        iconType: 'preset',
        iconPreset: 'settings',
        iconColor: '#faad14',
        iconBackground: '#fffbe6',
        iconBorder: '#ffe58f',
        iconSize: 14,
      },
    },
    {
      id: 'preset-5',
      type: 'icon-node',
      x: 350,
      y: 250,
      properties: {
        title: '安全验证',
        description: '身份认证检查',
        iconType: 'preset',
        iconPreset: 'lock',
        iconColor: '#ff4d4f',
        iconBackground: '#fff2f0',
        iconBorder: '#ffadd2',
        iconSize: 14,
      },
    },
    {
      id: 'preset-6',
      type: 'icon-node',
      x: 550,
      y: 250,
      properties: {
        title: '搜索功能',
        description: '全文检索服务',
        iconType: 'preset',
        iconPreset: 'search',
        iconColor: '#13c2c2',
        iconBackground: '#e6fffb',
        iconBorder: '#87e8de',
        iconSize: 14,
      },
    },
  ],
  edges: [],
};

/**
 * 自定义 SVG 路径示例
 */
export const customPathExamples: WorkflowData = {
  nodes: [
    {
      id: 'path-1',
      type: 'icon-node',
      x: 150,
      y: 150,
      properties: {
        title: '自定义星形',
        description: '使用 SVG 路径',
        iconType: 'path',
        iconPath:
          'M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z',
        iconColor: '#ff6b35',
        iconSize: 16,
      },
    },
    {
      id: 'path-2',
      type: 'icon-node',
      x: 350,
      y: 150,
      properties: {
        title: '闪电图标',
        description: '快速处理',
        iconType: 'path',
        iconPath: 'M13 2L3 14h9l-1 8 10-12h-9l1-8z',
        iconColor: '#fadb14',
        iconSize: 14,
      },
    },
    {
      id: 'path-3',
      type: 'icon-node',
      x: 550,
      y: 150,
      properties: {
        title: '齿轮组合',
        description: '复杂处理逻辑',
        iconType: 'path',
        iconPath:
          'M9.405 1.05c-.413-1.4-2.397-1.4-2.81 0l-.1.34a1.464 1.464 0 0 1-2.105.872l-.31-.17c-1.283-.698-2.686.705-1.987 1.987l.169.311c.446.82.023 1.841-.872 2.105l-.34.1c-1.4.413-1.4 2.397 0 2.81l.34.1a1.464 1.464 0 0 1 .872 2.105l-.17.31c-.698 1.283.705 2.686 1.987 1.987l.311-.169a1.464 1.464 0 0 1 2.105.872l.1.34c.413 1.4 2.397 1.4 2.81 0l.1-.34a1.464 1.464 0 0 1 2.105-.872l.31.17c1.283.698 2.686-.705 1.987-1.987l-.169-.311a1.464 1.464 0 0 1 .872-2.105l.34-.1c1.4-.413 1.4-2.397 0-2.81l-.34-.1a1.464 1.464 0 0 1-.872-2.105l.17-.31c.698-1.283-.705-2.686-1.987-1.987l-.311.169a1.464 1.464 0 0 1-2.105-.872l-.1-.34zM8 10.93a2.929 2.929 0 1 1 0-5.86 2.929 2.929 0 0 1 0 5.858z',
        iconColor: '#531dab',
        iconSize: 14,
      },
    },
  ],
  edges: [],
};

/**
 * 文本图标（Emoji）示例
 */
export const textIconExamples: WorkflowData = {
  nodes: [
    {
      id: 'text-1',
      type: 'icon-node',
      x: 150,
      y: 150,
      properties: {
        title: '火箭发射',
        description: '快速启动',
        iconType: 'text',
        iconText: '🚀',
        iconColor: '#ff4d4f',
        iconSize: 18,
      },
    },
    {
      id: 'text-2',
      type: 'icon-node',
      x: 350,
      y: 150,
      properties: {
        title: '数据分析',
        description: '图表统计',
        iconType: 'text',
        iconText: '📊',
        iconColor: '#1890ff',
        iconSize: 16,
      },
    },
    {
      id: 'text-3',
      type: 'icon-node',
      x: 550,
      y: 150,
      properties: {
        title: '警告提示',
        description: '注意事项',
        iconType: 'text',
        iconText: '⚠️',
        iconColor: '#faad14',
        iconSize: 16,
      },
    },
    {
      id: 'text-4',
      type: 'icon-node',
      x: 150,
      y: 250,
      properties: {
        title: '成功完成',
        description: '任务完成',
        iconType: 'text',
        iconText: '✅',
        iconColor: '#52c41a',
        iconSize: 16,
      },
    },
    {
      id: 'text-5',
      type: 'icon-node',
      x: 350,
      y: 250,
      properties: {
        title: '加载中',
        description: '处理中',
        iconType: 'text',
        iconText: '🔄',
        iconColor: '#13c2c2',
        iconSize: 16,
      },
    },
    {
      id: 'text-6',
      type: 'icon-node',
      x: 550,
      y: 250,
      properties: {
        title: '邮件通知',
        description: '发送邮件',
        iconType: 'text',
        iconText: '📧',
        iconColor: '#722ed1',
        iconSize: 16,
      },
    },
  ],
  edges: [],
};

/**
 * 混合类型示例 - 完整工作流
 */
export const mixedIconWorkflow: WorkflowData = {
  nodes: [
    // 开始节点
    {
      id: 'start-1',
      type: 'start',
      x: 100,
      y: 200,
      properties: {
        name: '开始',
      },
      text: {
        x: 100,
        y: 240,
        value: '开始',
      },
    },
    // 用户上传文件
    {
      id: 'upload-1',
      type: 'icon-node',
      x: 250,
      y: 200,
      properties: {
        title: '文件上传',
        description: '用户上传文档',
        iconType: 'preset',
        iconPreset: 'upload',
        iconColor: '#1890ff',
        iconSize: 14,
      },
    },
    // 文件验证
    {
      id: 'validate-1',
      type: 'icon-node',
      x: 400,
      y: 200,
      properties: {
        title: '格式验证',
        description: '检查文件格式',
        iconType: 'text',
        iconText: '🔍',
        iconColor: '#faad14',
        iconSize: 16,
      },
    },
    // 数据处理
    {
      id: 'process-1',
      type: 'icon-node',
      x: 550,
      y: 200,
      properties: {
        title: '数据处理',
        description: '解析和转换',
        iconType: 'path',
        iconPath:
          'M9.405 1.05c-.413-1.4-2.397-1.4-2.81 0l-.1.34a1.464 1.464 0 0 1-2.105.872l-.31-.17c-1.283-.698-2.686.705-1.987 1.987l.169.311c.446.82.023 1.841-.872 2.105l-.34.1c-1.4.413-1.4 2.397 0 2.81l.34.1a1.464 1.464 0 0 1 .872 2.105l-.17.31c-.698 1.283.705 2.686 1.987 1.987l.311-.169a1.464 1.464 0 0 1 2.105.872l.1.34c.413 1.4 2.397 1.4 2.81 0l.1-.34a1.464 1.464 0 0 1 2.105-.872l.31.17c1.283.698 2.686-.705 1.987-1.987l-.169-.311a1.464 1.464 0 0 1 .872-2.105l.34-.1c1.4-.413 1.4-2.397 0-2.81l-.34-.1a1.464 1.464 0 0 1-.872-2.105l.17-.31c.698-1.283-.705-2.686-1.987-1.987l-.311.169a1.464 1.464 0 0 1-2.105-.872l-.1-.34zM8 10.93a2.929 2.929 0 1 1 0-5.86 2.929 2.929 0 0 1 0 5.858z',
        iconColor: '#722ed1',
        iconSize: 12,
      },
    },
    // 保存到数据库
    {
      id: 'save-1',
      type: 'icon-node',
      x: 700,
      y: 200,
      properties: {
        title: '数据保存',
        description: '存储到数据库',
        iconType: 'preset',
        iconPreset: 'database',
        iconColor: '#52c41a',
        iconSize: 14,
      },
    },
    // 完成通知
    {
      id: 'notify-1',
      type: 'icon-node',
      x: 850,
      y: 200,
      properties: {
        title: '完成通知',
        description: '通知用户完成',
        iconType: 'text',
        iconText: '✅',
        iconColor: '#52c41a',
        iconSize: 16,
      },
    },
    // 结束节点
    {
      id: 'end-1',
      type: 'end',
      x: 1000,
      y: 200,
      properties: {
        name: '结束',
      },
      text: {
        x: 1000,
        y: 240,
        value: '结束',
      },
    },
  ],
  edges: [
    {
      id: 'edge-1',
      type: 'polyline',
      sourceNodeId: 'start-1',
      targetNodeId: 'upload-1',
      startPoint: { x: 120, y: 200 },
      endPoint: { x: 160, y: 200 },
      properties: {},
    },
    {
      id: 'edge-2',
      type: 'polyline',
      sourceNodeId: 'upload-1',
      targetNodeId: 'validate-1',
      startPoint: { x: 340, y: 200 },
      endPoint: { x: 310, y: 200 },
      properties: {},
    },
    {
      id: 'edge-3',
      type: 'polyline',
      sourceNodeId: 'validate-1',
      targetNodeId: 'process-1',
      startPoint: { x: 490, y: 200 },
      endPoint: { x: 460, y: 200 },
      properties: {},
    },
    {
      id: 'edge-4',
      type: 'polyline',
      sourceNodeId: 'process-1',
      targetNodeId: 'save-1',
      startPoint: { x: 640, y: 200 },
      endPoint: { x: 610, y: 200 },
      properties: {},
    },
    {
      id: 'edge-5',
      type: 'polyline',
      sourceNodeId: 'save-1',
      targetNodeId: 'notify-1',
      startPoint: { x: 790, y: 200 },
      endPoint: { x: 760, y: 200 },
      properties: {},
    },
    {
      id: 'edge-6',
      type: 'polyline',
      sourceNodeId: 'notify-1',
      targetNodeId: 'end-1',
      startPoint: { x: 940, y: 200 },
      endPoint: { x: 980, y: 200 },
      properties: {},
    },
  ],
};

/**
 * 所有图标类型的完整展示
 */
export const allIconTypesShowcase: WorkflowData = {
  nodes: [
    // 第一行：预定义图标
    {
      id: 'showcase-1',
      type: 'icon-node',
      x: 100,
      y: 100,
      properties: {
        title: '用户',
        description: 'user 图标',
        iconType: 'preset',
        iconPreset: 'user',
        iconColor: '#1890ff',
        iconSize: 12,
      },
    },
    {
      id: 'showcase-2',
      type: 'icon-node',
      x: 300,
      y: 100,
      properties: {
        title: '文件',
        description: 'file 图标',
        iconType: 'preset',
        iconPreset: 'file',
        iconColor: '#52c41a',
        iconSize: 12,
      },
    },
    {
      id: 'showcase-3',
      type: 'icon-node',
      x: 500,
      y: 100,
      properties: {
        title: '设置',
        description: 'settings 图标',
        iconType: 'preset',
        iconPreset: 'settings',
        iconColor: '#faad14',
        iconSize: 12,
      },
    },
    // 第二行：SVG 路径图标
    {
      id: 'showcase-4',
      type: 'icon-node',
      x: 100,
      y: 220,
      properties: {
        title: '自定义心形',
        description: 'SVG 路径',
        iconType: 'path',
        iconPath:
          'M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z',
        iconColor: '#ff4d4f',
        iconSize: 14,
      },
    },
    {
      id: 'showcase-5',
      type: 'icon-node',
      x: 300,
      y: 220,
      properties: {
        title: '自定义星形',
        description: 'SVG 路径',
        iconType: 'path',
        iconPath:
          'M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z',
        iconColor: '#722ed1',
        iconSize: 14,
      },
    },
    {
      id: 'showcase-6',
      type: 'icon-node',
      x: 500,
      y: 220,
      properties: {
        title: '自定义箭头',
        description: 'SVG 路径',
        iconType: 'path',
        iconPath: 'M5 12h14 M12 5l7 7-7 7',
        iconColor: '#13c2c2',
        iconSize: 14,
      },
    },
    // 第三行：文本图标（Emoji）
    {
      id: 'showcase-7',
      type: 'icon-node',
      x: 100,
      y: 340,
      properties: {
        title: '火箭',
        description: 'Emoji 图标',
        iconType: 'text',
        iconText: '🚀',
        iconColor: '#fa541c',
        iconSize: 16,
      },
    },
    {
      id: 'showcase-8',
      type: 'icon-node',
      x: 300,
      y: 340,
      properties: {
        title: '闪电',
        description: 'Emoji 图标',
        iconType: 'text',
        iconText: '⚡',
        iconColor: '#fadb14',
        iconSize: 16,
      },
    },
    {
      id: 'showcase-9',
      type: 'icon-node',
      x: 500,
      y: 340,
      properties: {
        title: '齿轮',
        description: 'Emoji 图标',
        iconType: 'text',
        iconText: '⚙️',
        iconColor: '#531dab',
        iconSize: 16,
      },
    },
  ],
  edges: [],
};

/**
 * 导出所有示例
 */
export const iconNodeExamples = {
  presetIconExamples,
  customPathExamples,
  textIconExamples,
  mixedIconWorkflow,
  allIconTypesShowcase,
};
