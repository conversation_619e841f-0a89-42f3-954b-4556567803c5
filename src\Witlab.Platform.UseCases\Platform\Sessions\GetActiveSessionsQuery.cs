using Ardalis.Result;

namespace Witlab.Platform.UseCases.Platform.Sessions;

/// <summary>
/// 获取活跃会话查询
/// </summary>
public record GetActiveSessionsQuery(
  int PageNumber = 1,
  int PageSize = 20
) : IQuery<Result<(List<UserSessionDTO> Sessions, int TotalCount)>>;

/// <summary>
/// 获取活跃会话查询处理器
/// </summary>
public class GetActiveSessionsQueryHandler : IQueryHandler<GetActiveSessionsQuery, Result<(List<UserSessionDTO> Sessions, int TotalCount)>>
{
  private readonly IUserSessionService _userSessionService;

  public GetActiveSessionsQueryHandler(IUserSessionService userSessionService)
  {
    _userSessionService = userSessionService;
  }

  public async Task<Result<(List<UserSessionDTO> Sessions, int TotalCount)>> Handle(GetActiveSessionsQuery request, CancellationToken cancellationToken)
  {
    var result = await _userSessionService.GetActiveSessionsAsync(request.PageNumber, request.PageSize);
    
    if (!result.IsSuccess)
    {
      return Result<(List<UserSessionDTO>, int)>.Error(result.Errors.ToArray());
    }

    var sessions = result.Value.Sessions;
    var sessionDTOs = sessions.Select(s => new UserSessionDTO(
      s.Id,
      s.UserId,
      s.UserName,
      s.TokenId,
      s.LoginTime,
      s.LastActivityTime,
      s.TokenExpiresAt,
      s.SessionInfo.IpAddress,
      s.SessionInfo.UserAgent,
      s.SessionInfo.DeviceType,
      s.SessionInfo.OperatingSystem,
      s.SessionInfo.Browser,
      s.SessionInfo.Location,
      s.Status.ToString(),
      s.LoginSource.ToString(),
      s.IsActive
    )).ToList();

    return Result<(List<UserSessionDTO>, int)>.Success((sessionDTOs, result.Value.TotalCount));
  }
}
