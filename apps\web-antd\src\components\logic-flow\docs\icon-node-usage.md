# Icon Node SVG 图标使用指南

## 概述

`icon-node` 现在支持多种类型的图标显示，包括 SVG 路径、预定义图标和文本图标（Emoji）。

## 图标类型

### 1. 预定义图标 (`iconType: 'preset'`)

使用内置的 SVG 图标库，包含常用的图标：

```typescript
const nodeData = {
  id: 'icon-1',
  type: 'icon-node',
  x: 200,
  y: 200,
  properties: {
    title: '用户管理',
    description: '管理系统用户',
    iconType: 'preset',
    iconPreset: 'user', // 可选值见下方列表
    iconColor: '#1890ff',
    iconSize: 14,
  },
};
```

#### 可用的预定义图标：

- **基础图标**: `star`, `heart`, `check`, `plus`, `search`
- **用户相关**: `user`, `settings`, `lock`, `key`
- **文件相关**: `file`, `mail`, `download`, `upload`
- **导航相关**: `home`, `map`, `warning`
- **数据相关**: `database`, `code`, `chart`
- **时间相关**: `clock`

### 2. 自定义 SVG 路径 (`iconType: 'path'`)

使用自定义的 SVG 路径字符串：

```typescript
const nodeData = {
  id: 'icon-2',
  type: 'icon-node',
  x: 400,
  y: 200,
  properties: {
    title: '自定义图标',
    description: '使用 SVG 路径',
    iconType: 'path',
    iconPath:
      'M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z',
    iconColor: '#52c41a',
    iconSize: 16,
  },
};
```

### 3. 文本图标 (`iconType: 'text'`)

使用 Emoji 或文本作为图标：

```typescript
const nodeData = {
  id: 'icon-3',
  type: 'icon-node',
  x: 600,
  y: 200,
  properties: {
    title: 'Emoji 图标',
    description: '使用 Emoji 作为图标',
    iconType: 'text',
    iconText: '🚀', // 可以是任何 Emoji 或文本
    iconColor: '#fa8c16',
    iconSize: 18,
  },
};
```

## 完整配置选项

```typescript
interface IconNodeProperties {
  // 基础信息
  title: string; // 节点标题
  description: string; // 节点描述

  // 图标配置
  iconType: 'preset' | 'path' | 'text'; // 图标类型
  iconColor: string; // 图标颜色
  iconSize: number; // 图标大小

  // 外观配置
  iconBackground: string; // 图标背景色
  iconBorder: string; // 图标边框色

  // 类型特定配置
  iconPreset?: string; // 预定义图标名称 (iconType: 'preset')
  iconPath?: string; // SVG 路径 (iconType: 'path')
  iconText?: string; // 文本内容 (iconType: 'text')
}
```

## 在工作流中使用

### 方式一：直接创建节点

```typescript
import { useWorkflowDesigner } from '@/components/logic-flow';

const { addNode } = useWorkflowDesigner();

// 添加预定义图标节点
addNode('icon-node', {
  properties: {
    title: '数据处理',
    description: '处理业务数据',
    iconType: 'preset',
    iconPreset: 'database',
    iconColor: '#722ed1',
  },
});
```

### 方式二：在工作流数据中定义

```typescript
const workflowData = {
  nodes: [
    {
      id: 'process-1',
      type: 'icon-node',
      x: 200,
      y: 150,
      properties: {
        title: '文件上传',
        description: '上传业务文件',
        iconType: 'preset',
        iconPreset: 'upload',
        iconColor: '#1890ff',
        iconBackground: '#e6f7ff',
        iconBorder: '#91d5ff',
        iconSize: 14,
      },
    },
    {
      id: 'process-2',
      type: 'icon-node',
      x: 400,
      y: 150,
      properties: {
        title: '数据验证',
        description: '验证数据格式',
        iconType: 'text',
        iconText: '✅',
        iconColor: '#52c41a',
        iconSize: 16,
      },
    },
  ],
  edges: [],
};
```

## 获取 SVG 路径

### 从图标库获取

1. **Heroicons**: https://heroicons.com/
2. **Lucide**: https://lucide.dev/
3. **Feather Icons**: https://feathericons.com/
4. **Material Icons**: https://fonts.google.com/icons

### 使用工具转换

```bash
# 使用 svg2jsx 转换 SVG 到路径
npm install -g svg2jsx
svg2jsx your-icon.svg
```

### 示例：从 Heroicons 获取

```html
<!-- 原始 SVG -->
<svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
  <path d="M9 12l2 2 4-4" />
</svg>

<!-- 提取路径 -->
iconPath: "M9 12l2 2 4-4"
```

## 样式自定义

### 主题色配置

```typescript
// 成功状态
{
  iconColor: '#52c41a',
  iconBackground: '#f6ffed',
  iconBorder: '#b7eb8f',
}

// 警告状态
{
  iconColor: '#faad14',
  iconBackground: '#fffbe6',
  iconBorder: '#ffe58f',
}

// 错误状态
{
  iconColor: '#ff4d4f',
  iconBackground: '#fff2f0',
  iconBorder: '#ffadd2',
}
```

### 尺寸配置

```typescript
// 小尺寸
{
  iconSize: 10;
}

// 中等尺寸（默认）
{
  iconSize: 12;
}

// 大尺寸
{
  iconSize: 16;
}
```

## 最佳实践

1. **一致性**: 在同一个工作流中保持图标风格一致
2. **语义化**: 选择与功能相关的图标
3. **对比度**: 确保图标颜色与背景有足够对比度
4. **尺寸**: 根据节点大小调整图标尺寸
5. **性能**: 优先使用预定义图标，复杂 SVG 路径可能影响性能

## 常见问题

### Q: 图标显示不正确？

A: 检查 `iconType` 是否正确，确保对应的属性已设置（如 `iconPreset` 或 `iconPath`）。

### Q: 自定义 SVG 路径不显示？

A: 确保 SVG 路径语法正确，路径应该适合 24x24 的画布尺寸。

### Q: 如何获取更多图标？

A: 可以从图标库网站复制 SVG 路径，或者使用现有的图标字体库。

### Q: 可以使用彩色图标吗？

A: 当前版本只支持单色图标，彩色图标需要自定义实现。
