<script setup lang="ts">
import type { Arrayable } from '@vueuse/core';
import type { DataNode } from 'ant-design-vue/es/tree';
import type {
  BaseOptionType,
  LabelInValueType,
  RawValueType,
} from 'ant-design-vue/es/vc-select/Select';
import type { ChangeEventExtra } from 'ant-design-vue/es/vc-tree-select/interface';

import type {
  Approver,
  ConditionRule,
  WitLabStep,
  WitLabStepAction,
  WorkflowEdge,
  WorkflowNode,
} from '../types/workflow';

import { computed, ref, watch } from 'vue';

import { VbenTree } from '@vben/common-ui';
import { cloneDeep } from '@vben/utils';

import {
  Button,
  Checkbox,
  Col,
  Divider,
  Empty,
  Form,
  Input,
  InputNumber,
  message,
  Radio,
  RadioGroup,
  Row,
  Select,
  Space,
  Spin,
  Textarea,
  TreeSelect,
} from 'ant-design-vue';
import { Delete, Plus } from 'lucide-vue-next';

import { getMenuList } from '#/api/system/menu';
import { $t } from '#/locales';
import { isNullOrUnDef } from '#/utils';

import { CONDITION_OPERATORS } from '../config/nodes';

interface Props {
  element: WorkflowEdge | WorkflowNode;
  elementType: 'edge' | 'node';
  approverDataSource?: Approver[];
}

const props = defineProps<Props>();

const emit = defineEmits<{
  confirm: [element: WorkflowEdge | WorkflowNode];
  update: [element: WorkflowEdge | WorkflowNode];
}>();

// 响应式数据 - 节点表单（WitLabStep）
const nodeForm = ref({
  stepCode: '',
  stepName: '',
  displayStatus: '',
  signatureType: 'Esig' as 'Esig' | 'Silent',
  commentName: '',
  // 保留原有字段用于兼容
  name: '',
  description: '',
  approvers: [] as string[],
  approvalType: 'single',
  timeLimit: 24,
  autoApprove: false,
  conditions: [] as ConditionRule[],
  branches: [] as string[],
  menus: [] as string[] | undefined,
  permissions: [] as string[] | undefined,
});

// 响应式数据 - 连线表单（WitLabStepAction）
const edgeForm = ref({
  sorter: 1,
  dispositionCode: '',
  dispositionName: '',
  isReject: 'N',
  dispFlag: 'Y',
  requireReason: 'N',
  stepDetCat: '',
  // 保留原有字段用于兼容
  label: '',
  condition: '',
  description: '',
  priority: 'normal' as 'high' | 'low' | 'normal' | 'urgent',
  style: {
    stroke: '#1890ff',
    strokeWidth: 2,
    strokeDasharray: '',
    animation: false,
  },
  validation: {
    required: false,
    message: '',
  },
  tags: [] as string[],
});

const nodeFormRef = ref();
const edgeFormRef = ref();

// 计算属性
const nodeElement = computed(() =>
  props.elementType === 'node' ? (props.element as WorkflowNode) : null,
);

const edgeElement = computed(() =>
  props.elementType === 'edge' ? (props.element as WorkflowEdge) : null,
);

const approverOptions = computed(
  () =>
    props.approverDataSource?.map((approver) => ({
      label: approver.name,
      value: approver.id,
    })) || [],
);

const operatorOptions = computed(() =>
  CONDITION_OPERATORS.map((op) => ({
    label: op.label,
    value: op.value,
  })),
);

// 签名类型选项
const signatureTypeOptions = [
  { label: '电子签名', value: 'Esig' },
  { label: '静默签名', value: 'Silent' },
];

// 是否选项
const yesNoOptions = [
  { label: '是', value: 'Y' },
  { label: '否', value: 'N' },
];

// 动作分类选项
const stepDetCatOptions = [
  { label: '审批', value: 'APPROVAL' },
  { label: '通知', value: 'NOTIFICATION' },
  { label: '自动', value: 'AUTO' },
  { label: '手动', value: 'MANUAL' },
];

const loadingPermissions = ref(false);
const permissions = ref<DataNode[]>([]);

async function loadPermissions() {
  loadingPermissions.value = true;
  try {
    const res = await getMenuList();
    permissions.value = res as unknown as DataNode[];
  } finally {
    loadingPermissions.value = false;
  }
}

// 监听元素变化
watch(
  () => props.element,
  (newElement) => {
    if (props.elementType === 'node') {
      if (permissions.value.length === 0) {
        loadPermissions();
      }

      const node = newElement as WorkflowNode;
      const witLabData = node._data as WitLabStep;

      nodeForm.value = {
        // WitLabStep 字段
        stepCode: witLabData?.STEPCODE || node.properties.stepCode || '',
        stepName: witLabData?.STEPNAME || node.properties.stepName || '',
        displayStatus:
          witLabData?.STEPDISPSTATUS || node.properties.displayStatus || '',
        signatureType: (witLabData?.SIGNATURETYPE ||
          node.properties.signatureType ||
          'Esig') as 'Esig' | 'Silent',
        commentName:
          witLabData?.COMMENTNAME || node.properties.commentName || '',
        menus: witLabData?.MENUS || [],
        permissions: witLabData?.PERMISSIONS || [],

        // 兼容原有字段
        name: node.properties.name || '',
        description: node.properties.description || '',
        approvers: node.properties.approvers?.map((a) => a.id) || [],
        approvalType: node.properties.approvalType || 'single',
        timeLimit: node.properties.timeLimit || 24,
        autoApprove: node.properties.autoApprove || false,
        conditions: node.properties.conditions || [],
        branches: node.properties.branches || [],
      };
    } else {
      const edge = newElement as WorkflowEdge;
      const witLabData = edge._data as WitLabStepAction;

      edgeForm.value = {
        // WitLabStepAction 字段
        sorter: witLabData?.SORTER || edge.properties?.sorter || 1,
        dispositionCode:
          witLabData?.DISPOSITIONCODE || edge.properties?.dispositionCode || '',
        dispositionName:
          witLabData?.DISPOSITIONNAME || edge.properties?.dispositionName || '',
        isReject: witLabData?.ISREJECT || edge.properties?.isReject || 'N',
        dispFlag: witLabData?.DISPFLAG || edge.properties?.dispFlag || 'Y',
        requireReason:
          witLabData?.REQUIREREASON || edge.properties?.requireReason || 'N',
        stepDetCat: witLabData?.STEPDETCAT || edge.properties?.stepDetCat || '',

        // 兼容原有字段
        label: edge.properties?.label || '',
        condition: edge.properties?.condition || '',
        description: edge.properties?.description || '',
        priority: edge.properties?.priority || 'normal',
        style: {
          stroke: edge.properties?.style?.stroke || '#1890ff',
          strokeWidth: edge.properties?.style?.strokeWidth || 2,
          strokeDasharray: edge.properties?.style?.strokeDasharray || '',
          animation: edge.properties?.style?.animation || false,
        },
        validation: {
          required: edge.properties?.validation?.required || false,
          message: edge.properties?.validation?.message || '',
        },
        tags: edge.properties?.tags || [],
      };
    }
  },
  { immediate: true },
);

// 方法
const handleUpdate = () => {
  if (props.elementType === 'node') {
    const node = { ...nodeElement.value! };
    // console.log(node);
    // 更新 WitLabStep 相关属性
    node.properties = {
      ...node.properties,
      name: nodeForm.value.displayStatus,

      // 保留兼容字段
      // name: nodeForm.value.displayStatus,
      // description: nodeForm.value.description,
    };

    node.text = {
      ...node.text,
      x: node.text?.x ?? node.x,
      y: node.text?.y ?? node.y + 50,
      value: nodeForm.value.stepName,
    };

    if (node.type === 'approval') {
      node.properties.approvers = nodeForm.value.approvers.map((id) => {
        const approver = props.approverDataSource?.find((a) => a.id === id);
        return approver || { id, name: id };
      });
      node.properties.approvalType = nodeForm.value.approvalType as
        | 'all'
        | 'majority'
        | 'single';
      node.properties.timeLimit = nodeForm.value.timeLimit;
      node.properties.autoApprove = nodeForm.value.autoApprove;
    }

    if (node.type === 'condition') {
      node.properties.conditions = nodeForm.value.conditions;
    }

    if (node.type === 'parallel') {
      node.properties.branches = nodeForm.value.branches;
    }

    // 更新 _data 字段
    const witLabData = node._data as WitLabStep;
    node._data = {
      ...witLabData,
      STEPCODE: nodeForm.value.stepCode,
      STEPNAME: nodeForm.value.stepName,
      STEPDISPSTATUS: nodeForm.value.displayStatus,
      SIGNATURETYPE: nodeForm.value.signatureType,
      COMMENTNAME: nodeForm.value.commentName,
      MENUS: nodeForm.value.menus,
      PERMISSIONS: nodeForm.value.permissions,
    };

    emit('update', node);
    return node;
  } else {
    const edge = { ...edgeElement.value! };

    // 更新 WitLabStepAction 相关属性
    edge.properties = {
      ...edge.properties,

      // 保留兼容字段
      condition: edgeForm.value.condition,
      description: edgeForm.value.description,
      priority: edgeForm.value.priority,
      style: edgeForm.value.style,
      validation: edgeForm.value.validation,
      tags: edgeForm.value.tags,
    };
    edge.text = {
      x: edge.text?.x ?? 0,
      y: edge.text?.y ?? 0,
      ...edge.text,
      value: edgeForm.value.dispositionName,
    };

    // 更新 _data 字段
    const witLabData = edge._data as WitLabStepAction;
    edge._data = {
      ...witLabData,
      SORTER: edgeForm.value.sorter,
      DISPOSITIONCODE: edgeForm.value.dispositionCode,
      DISPOSITIONNAME: edgeForm.value.dispositionName,
      ISREJECT: edgeForm.value.isReject,
      DISPFLAG: edgeForm.value.dispFlag,
      REQUIREREASON: edgeForm.value.requireReason,
      STEPDETCAT: edgeForm.value.stepDetCat,
    };

    emit('update', edge);
    return edge;
  }
};

const addCondition = () => {
  nodeForm.value.conditions.push({
    field: '',
    operator: 'eq',
    value: '',
  });
  handleUpdate();
};

const removeCondition = (index: number) => {
  nodeForm.value.conditions.splice(index, 1);
  handleUpdate();
};

const addBranch = () => {
  const branchCount = nodeForm.value.branches.length;
  nodeForm.value.branches.push(`分支${branchCount + 1}`);
  handleUpdate();
};

const removeBranch = (index: number) => {
  nodeForm.value.branches.splice(index, 1);
  handleUpdate();
};

const handleReset = () => {
  // 重置表单到初始状态
  watch(
    () => props.element,
    () => {},
    { immediate: true },
  );
  message.info('已重置');
};

const handleSave = async () => {
  try {
    props.elementType === 'node'
      ? await nodeFormRef.value?.validate()
      : await edgeFormRef.value?.validate();
    const updatedElement = handleUpdate();
    emit('confirm', updatedElement);
    message.success('保存成功');
  } catch {
    // 校验失败自动提示，无需额外处理
  }
};

/**
 * 递归处理菜单数据，提取嵌套字段
 */
const processMenuData = (menuData: any[]): any[] => {
  if (!menuData || !Array.isArray(menuData)) return [];

  return menuData.map((menu) => {
    const processedMenu = { ...menu };

    // 提取 meta.title 并翻译
    if (menu?.meta?.title) {
      processedMenu.title = $t(menu.meta.title);
      processedMenu.label = $t(menu.meta.title);
    } else {
      processedMenu.title = menu?.title || menu?.name || menu?.label || '';
      processedMenu.label = menu?.title || menu?.name || menu?.label || '';
    }

    // 递归处理子节点
    if (menu.children && Array.isArray(menu.children)) {
      processedMenu.children = processMenuData(menu.children);
      // 对于菜单类型，清空其子节点
      if (processedMenu.type === 'menu') {
        processedMenu.children = [];
      }
    }

    return processedMenu;
  });
};

/**
 * 自定义树节点过滤函数
 */
const filterTreeNode = (inputValue: string, treeNode: any) => {
  if (!inputValue) return true;

  const searchValue = inputValue.toLowerCase();

  // 获取节点标题
  const getNodeTitle = (node: any): string => {
    if (node?.title) return node.title;
    if (node?.label) return node.label;
    if (node?.meta?.title) return $t(node.meta.title);
    return node?.name || '';
  };

  const nodeTitle = getNodeTitle(treeNode).toLowerCase();
  return nodeTitle.includes(searchValue);
};

// 处理后的菜单数据
const linkMenus = computed(() => {
  const cloneMenus = cloneDeep(permissions.value);
  const processed = processMenuData(cloneMenus);

  return processed;
});

const handleMenuSelect = (
  value: LabelInValueType | RawValueType,
  option: BaseOptionType,
) => {
  const menu = option;
  if (menu.type !== 'menu') {
    nodeForm.value.menus?.splice(nodeForm.value.menus.indexOf(menu.name), 1);
  }
};

const handleMenuChange = (
  values: string[],
  labelList: string[],
  extra: ChangeEventExtra,
) => {
  console.log({ values, labelList, extra });
  nodeForm.value.permissions = nodeForm.value.permissions?.filter((p) => {
    const menu = stepPermissions.value.find((m) => m.authCode === p);
    return !isNullOrUnDef(menu) && values.includes(menu?.name);
  });
  console.log(nodeForm.value.permissions);
};

const stepPermissions = computed<DataNode[]>(() => {
  const cloneMenus = cloneDeep(permissions.value);

  const processedMenus = cloneMenus.flatMap((menu) => {
    if (
      menu.children?.some((cm) => nodeForm.value.menus?.includes(cm.name)) &&
      menu.children?.some(
        (cm) => cm.children?.length && cm.children?.length > 0,
      )
    ) {
      return menu.children.filter((cm) =>
        nodeForm.value.menus?.includes(cm.name),
      );
    }
    return [];
  });

  return processedMenus ?? [];
});

const handlePermissionUpdate = (
  val: Arrayable<number | string> | undefined,
) => {
  nodeForm.value.permissions = val as string[];
};
</script>

<template>
  <div class="workflow-property-panel">
    <!-- 节点属性 -->
    <div v-if="elementType === 'node'" class="property-content">
      <Form ref="nodeFormRef" :model="nodeForm" layout="vertical">
        <!-- WitLabStep 字段 -->
        <Divider>步骤配置</Divider>

        <Form.Item
          label="步骤代码"
          name="stepCode"
          :rules="[{ required: true, message: '步骤代码为必填项' }]"
        >
          <Input
            v-model:value="nodeForm.stepCode"
            placeholder="请输入步骤代码"
          />
        </Form.Item>

        <Form.Item label="步骤名称">
          <Input
            v-model:value="nodeForm.stepName"
            placeholder="请输入步骤名称"
          />
        </Form.Item>

        <Form.Item label="显示状态">
          <Input
            v-model:value="nodeForm.displayStatus"
            placeholder="请输入显示状态"
          />
        </Form.Item>

        <Form.Item label="签名类型">
          <Select
            v-model:value="nodeForm.signatureType"
            placeholder="请选择签名类型"
            :options="signatureTypeOptions"
          />
        </Form.Item>

        <Form.Item label="备注名称">
          <Input
            v-model:value="nodeForm.commentName"
            placeholder="请输入备注名称"
          />
        </Form.Item>

        <Form.Item label="关联菜单">
          <TreeSelect
            v-model:value="nodeForm.menus"
            show-search
            allow-clear
            multiple
            tree-default-expand-all
            style="width: 100%"
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            placeholder="请选择关联菜单"
            :tree-data="linkMenus"
            :filter-tree-node="filterTreeNode"
            :field-names="{
              children: 'children',
              label: 'title',
              value: 'name',
            }"
            @select="handleMenuSelect"
            @change="handleMenuChange"
          >
            <template #title="node">
              <IconifyIcon v-if="node?.meta?.icon" :icon="node?.meta?.icon" />
              {{ node.title || node.label }}
            </template>
          </TreeSelect>
        </Form.Item>

        <Form.Item label="步骤权限">
          <Spin :spinning="loadingPermissions" wrapper-class-name="w-full">
            <template v-if="stepPermissions.length > 0">
              <VbenTree
                :tree-data="stepPermissions"
                multiple
                bordered
                :default-expanded-level="2"
                :model-value="nodeForm.permissions"
                @update:model-value="handlePermissionUpdate"
                value-field="authCode"
                label-field="meta.title"
                icon-field="meta.icon"
              >
                <template #node="{ value }">
                  <IconifyIcon v-if="value.meta.icon" :icon="value.meta.icon" />
                  {{ $t(value.meta.title) }}
                </template>
              </VbenTree>
            </template>
            <template v-else>
              <Empty description="请确认关联菜单下是否存在权限设置" />
            </template>
          </Spin>
        </Form.Item>

        <!-- 基础属性 -->
        <template v-if="false">
          <Divider>基础属性</Divider>

          <Form.Item label="节点名称">
            <Input v-model:value="nodeForm.name" placeholder="请输入节点名称" />
          </Form.Item>

          <Form.Item label="节点描述">
            <Textarea
              v-model:value="nodeForm.description"
              placeholder="请输入节点描述"
              :rows="3"
            />
          </Form.Item>
        </template>

        <!-- 审批节点特有属性 -->
        <template v-if="nodeElement?.type === 'approval'">
          <Divider>审批设置</Divider>

          <Form.Item label="审批人">
            <Select
              v-model:value="nodeForm.approvers"
              mode="multiple"
              placeholder="请选择审批人"
              :options="approverOptions"
            />
          </Form.Item>

          <Form.Item label="审批类型">
            <RadioGroup v-model:value="nodeForm.approvalType">
              <Radio value="single">单人审批</Radio>
              <Radio value="all">全部审批</Radio>
              <Radio value="majority">多数审批</Radio>
            </RadioGroup>
          </Form.Item>

          <Form.Item label="审批时限（小时）">
            <InputNumber
              v-model:value="nodeForm.timeLimit"
              :min="1"
              :max="720"
              placeholder="24"
              style="width: 100%"
            />
          </Form.Item>

          <Form.Item>
            <Checkbox v-model:checked="nodeForm.autoApprove">
              超时自动审批
            </Checkbox>
          </Form.Item>
        </template>

        <!-- 条件节点特有属性 -->
        <template v-if="nodeElement?.type === 'condition'">
          <Divider>条件设置</Divider>

          <div class="condition-rules">
            <div
              v-for="(condition, index) in nodeForm.conditions"
              :key="index"
              class="condition-rule"
            >
              <Row :gutter="8">
                <Col :span="8">
                  <Input v-model:value="condition.field" placeholder="字段名" />
                </Col>
                <Col :span="6">
                  <Select
                    v-model:value="condition.operator"
                    placeholder="操作符"
                    :options="operatorOptions"
                  />
                </Col>
                <Col :span="8">
                  <Input v-model:value="condition.value" placeholder="值" />
                </Col>
                <Col :span="2">
                  <Button
                    type="text"
                    danger
                    size="small"
                    @click="removeCondition(index)"
                  >
                    <template #icon><Delete class="h-4 w-4" /></template>
                  </Button>
                </Col>
              </Row>
            </div>

            <Button type="dashed" block @click="addCondition">
              <template #icon><Plus class="h-4 w-4" /></template>
              添加条件
            </Button>
          </div>
        </template>

        <!-- 并行节点特有属性 -->
        <template v-if="nodeElement?.type === 'parallel'">
          <Divider>并行设置</Divider>

          <div class="branch-settings">
            <div
              v-for="(branch, index) in nodeForm.branches"
              :key="index"
              class="branch-item"
            >
              <Input
                v-model:value="nodeForm.branches[index]"
                :placeholder="`分支 ${index + 1}`"
              />
              <Button
                v-if="nodeForm.branches.length > 2"
                type="text"
                danger
                size="small"
                @click="removeBranch(index)"
              >
                <template #icon><Delete /></template>
              </Button>
            </div>

            <Button type="dashed" block @click="addBranch">
              <template #icon><Plus /></template>
              添加分支
            </Button>
          </div>
        </template>
      </Form>
    </div>

    <!-- 连线属性 -->
    <div v-else-if="elementType === 'edge'" class="property-content">
      <Form ref="edgeFormRef" :model="edgeForm" layout="vertical">
        <!-- WitLabStepAction 字段 -->
        <Divider>动作配置</Divider>

        <Form.Item label="排序">
          <InputNumber
            v-model:value="edgeForm.sorter"
            :min="1"
            placeholder="请输入排序"
            style="width: 100%"
          />
        </Form.Item>

        <Form.Item
          label="动作代码"
          name="dispositionCode"
          :rules="[{ required: true, message: '动作代码为必填项' }]"
        >
          <Input
            v-model:value="edgeForm.dispositionCode"
            placeholder="请输入动作代码"
          />
        </Form.Item>

        <Form.Item label="动作名称">
          <Input
            v-model:value="edgeForm.dispositionName"
            placeholder="请输入动作名称"
          />
        </Form.Item>

        <Form.Item label="是否退回？">
          <Select
            v-model:value="edgeForm.isReject"
            placeholder="请选择是否退回"
            :options="yesNoOptions"
          />
        </Form.Item>

        <Form.Item label="是否显示？">
          <Select
            v-model:value="edgeForm.dispFlag"
            placeholder="请选择是否显示"
            :options="yesNoOptions"
          />
        </Form.Item>

        <Form.Item label="原因必填？">
          <Select
            v-model:value="edgeForm.requireReason"
            placeholder="请选择原因是否必填"
            :options="yesNoOptions"
          />
        </Form.Item>

        <Form.Item label="动作分类">
          <Select
            v-model:value="edgeForm.stepDetCat"
            placeholder="请选择动作分类"
            :options="stepDetCatOptions"
            allow-clear
          />
        </Form.Item>

        <!-- 基础属性 -->

        <template v-if="false">
          <Divider>基础属性</Divider>

          <Form.Item label="连线标签">
            <Input
              v-model:value="edgeForm.label"
              placeholder="请输入连线标签"
            />
          </Form.Item>

          <Form.Item label="连线描述">
            <Textarea
              v-model:value="edgeForm.description"
              placeholder="请输入连线描述"
              :rows="2"
            />
          </Form.Item>

          <Form.Item label="条件表达式">
            <Textarea
              v-model:value="edgeForm.condition"
              placeholder="请输入条件表达式"
              :rows="3"
            />
          </Form.Item>

          <Divider>样式设置</Divider>

          <Form.Item label="优先级">
            <Select
              v-model:value="edgeForm.priority"
              placeholder="请选择优先级"
            >
              <Select.Option value="low">低</Select.Option>
              <Select.Option value="normal">普通</Select.Option>
              <Select.Option value="high">高</Select.Option>
              <Select.Option value="urgent">紧急</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item label="线条颜色">
            <Input
              v-model:value="edgeForm.style.stroke"
              placeholder="#1890ff"
            />
          </Form.Item>

          <Form.Item label="线条宽度">
            <InputNumber
              v-model:value="edgeForm.style.strokeWidth"
              :min="1"
              :max="10"
              placeholder="2"
              style="width: 100%"
            />
          </Form.Item>

          <Form.Item label="线条样式">
            <Select
              v-model:value="edgeForm.style.strokeDasharray"
              placeholder="请选择线条样式"
            >
              <Select.Option value="">实线</Select.Option>
              <Select.Option value="5,5">虚线</Select.Option>
              <Select.Option value="2,2">点线</Select.Option>
              <Select.Option value="10,5,2,5">点划线</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item>
            <Checkbox v-model:checked="edgeForm.style.animation">
              启用动画效果
            </Checkbox>
          </Form.Item>

          <Divider>验证设置</Divider>

          <Form.Item>
            <Checkbox v-model:checked="edgeForm.validation.required">
              必须经过此连线
            </Checkbox>
          </Form.Item>

          <Form.Item v-if="edgeForm.validation.required" label="验证消息">
            <Input
              v-model:value="edgeForm.validation.message"
              placeholder="请输入验证失败时的消息"
            />
          </Form.Item>

          <Form.Item label="标签">
            <Select
              v-model:value="edgeForm.tags"
              mode="tags"
              placeholder="请输入标签"
            />
          </Form.Item>
        </template>
      </Form>
    </div>

    <!-- 操作按钮 -->
    <div class="property-actions">
      <Space>
        <Button @click="handleReset">重置</Button>
        <Button type="primary" @click="handleSave">保存</Button>
      </Space>
    </div>
  </div>
</template>

<style scoped>
.workflow-property-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.property-content {
  flex: 1;
  overflow-y: auto;
}

.condition-rules {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.condition-rule {
  padding: 8px;
  background: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
}

.branch-settings {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.branch-item {
  display: flex;
  gap: 8px;
  align-items: center;
}

.property-actions {
  display: flex;
  justify-content: flex-end;
  padding: 16px;
  background: #fafafa;
  border-top: 1px solid #e8e8e8;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-form-item-label) {
  font-size: 13px;
  font-weight: 500;
}

:deep(.ant-divider) {
  margin: 16px 0 12px;
  font-size: 12px;
  color: #8c8c8c;
}
</style>
