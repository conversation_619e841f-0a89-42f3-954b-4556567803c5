<script lang="ts" setup>
import type { Recordable } from '@vben/types';

import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';

import { ref } from 'vue';

import { Page, useVbenForm, useVbenModal, z } from '@vben/common-ui';

import { Button, message, Space } from 'ant-design-vue';
import { EditIcon } from 'lucide-vue-next';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { callServer, updateProvider } from '#/api';
import { getWorkflows } from '#/api/settings';
import { showAduitViewer } from '#/components/audit-viewer';
import { $t } from '#/locales';
import { saveEditingRowOriginalData } from '#/utils/lims-grids-config';

import FlowContainer from './flow-container.vue';

const currentWorkflowRow = ref<any>(null);

const addWorkflowStep = ref(1);

const [FlowModal, flowModalApi] = useVbenModal({
  connectedComponent: FlowContainer,
  destroyOnClose: true,
});

const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: [
    {
      component: 'Input',
      fieldName: 'STEP',
      label: $t('settings.workflow.workflowCode'),
      rules: z.number().min(1).max(2),
      defaultValue: 1,
      dependencies: {
        show: false,
        triggerFields: ['STEP'],
      },
    },
    {
      component: 'Input',
      fieldName: 'WORKFLOWCODE',
      label: $t('settings.workflow.workflowCode'),
      rules: z
        .string()
        .min(
          2,
          $t('ui.formRules.minLength', [
            $t('settings.workflow.workflowCode'),
            2,
          ]),
        )
        .max(
          20,
          $t('ui.formRules.maxLength', [
            $t('settings.workflow.workflowCode'),
            20,
          ]),
        ),
      dependencies: {
        show: (values) => values.STEP === 1,
        triggerFields: ['STEP'],
      },
    },
    {
      component: 'Input',
      fieldName: 'STEPCODE',
      label: $t('settings.workflow.stepCode'),
      rules: z
        .string()
        .min(
          2,
          $t('ui.formRules.minLength', [$t('settings.workflow.stepCode'), 2]),
        )
        .max(
          20,
          $t('ui.formRules.maxLength', [$t('settings.workflow.stepCode'), 20]),
        ),
      dependencies: {
        show: (values) => values.STEP === 2,
        triggerFields: ['STEP'],
      },
    },
    {
      component: 'Textarea',
      componentProps: {
        maxLength: 200,
        showCount: true,
        rows: 3,
        class: 'w-full',
      },
      fieldName: 'WORKFLOWDESC',
      label: $t('settings.workflow.workflowDesc'),
      rules: z
        .string()
        .max(
          500,
          $t('ui.formRules.maxLength', [
            $t('settings.workflow.workflowDesc'),
            500,
          ]),
        )
        .optional(),
      dependencies: {
        show: (values) => values.STEP === 1,
        triggerFields: ['STEP'],
      },
    },
  ],
  showDefaultActions: false,
});

const [FormModal, formModalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (valid) {
      formModalApi.lock();
      const data = await formApi.getValues();
      try {
        const nOrigRec = await callServer('Areas.AddArea', [
          data.DEPT,
          data.AREA_NAME,
          data.AREA_DESCRIPTION,
          data.COST_CENTER,
          data.EXTERNAL_AREA_CODE,
          data.OTHER_ID,
        ]);
        if (nOrigRec) {
          message.success(
            $t('ui.actionMessage.operationSuccess', [data.AREA_NAME]),
          );
        }
        formModalApi.close();
        workflowGridApi.query();
      } catch {
        message.error($t('ui.actionMessage.operationFailed', [data.AREA_NAME]));
        resetForm();
        return;
      } finally {
        formModalApi.lock(false);
      }
    }
  },
  async onCancel() {
    const data = await formApi.getValues();
    await cancelAddWorkflow(data.WORKFLOWCODE);
    formModalApi.close();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      resetForm();
    }
  },
  draggable: true,
  showConfirmButton: false,
  closeOnClickModal: false,
  closable: false,
});

function resetForm() {
  addWorkflowStep.value = 1;
  formApi.resetForm();
  formApi.setValues({});
}

async function addWorkflowSecondStep() {
  const { valid } = await formApi.validateField('WORKFLOWCODE');
  if (valid) {
    const data = await formApi.getValues();
    try {
      const res = await callServer('GeneralWorkFlowManager.AddWorkFlow', [
        data.WORKFLOWCODE,
        null,
        data.WORKFLOWDESC,
      ]);
      if (res) {
        addWorkflowStep.value = 2;
        formApi.setFieldValue('STEP', addWorkflowStep.value);
      } else {
        message.error(
          $t('ui.formRules.alreadyExists', [
            $t('settings.workflow.workflowCode'),
            data.WORKFLOWCODE,
          ]),
        );
      }
    } finally {
      formModalApi.lock(false);
    }
  }
}

async function addWorkflowDone() {
  const { valid } = await formApi.validateField('STEPCODE');
  if (valid) {
    const data = await formApi.getValues();
    const res = await callServer('GeneralWorkFlowManager.AddWorkFlowStep', [
      data.WORKFLOWCODE,
      data.STEPCODE,
    ]);
    if (res) {
      message.success($t('ui.actionMessage.operationSuccess', [data.STEPCODE]));
      formModalApi.close();
      workflowGridApi.query();
    } else {
      message.error(
        $t('ui.formRules.alreadyExists', [
          $t('settings.workflow.stepCode'),
          data.STEPCODE,
        ]),
      );
    }
  }
}

async function cancelAddWorkflow(workflowCode?: string) {
  if (workflowCode) {
    await callServer('GeneralWorkFlowManager.CancelAddWorkflow', [
      workflowCode,
    ]);
  }
}

const [WorkflowGrid, workflowGridApi] = useVbenVxeGrid({
  gridEvents: {
    currentRowChange: (params: any) => {
      currentWorkflowRow.value = params.row;
    },
    editActivated: saveEditingRowOriginalData,
    editClosed: updateProvider,
    cellMenu: ({ row, $grid }) => $grid.setCurrentRow(row),
    menuClick: ({ menu }) => {
      if (menu.code === 'viewAudit') {
        const currentRow = workflowGridApi.grid?.getCurrentRecord();
        if (currentRow) {
          showAduitViewer({
            tableName: workflowGridApi.grid.params.tableName,
            origrec: currentRow.ORIGREC,
          });
        }
      }
    },
  },
  gridOptions: {
    columns: [
      {
        align: 'left',
        field: 'ORIGREC',
        title: $t('commons.origrec'),
        visible: false,
        minWidth: 150,
      },
      {
        field: 'WORKFLOWCODE',
        title: $t('settings.workflow.workflowCode'),
        minWidth: 200,
      },
      {
        field: 'WORKFLOWNAME',
        title: $t('settings.workflow.workflowName'),
        minWidth: 200,
        editRender: { name: 'VxeInput' },
      },
      {
        field: 'WORKFLOWDESC',
        title: $t('settings.workflow.workflowDesc'),
        minWidth: 250,
        editRender: { name: 'VxeInput' },
      },
      {
        field: 'APPLICATIONREF',
        title: $t('settings.workflow.applicationRef'),
        minWidth: 100,
        visible: false,
        editRender: { name: 'VxeInput' },
      },
      {
        field: 'TABLERELATED',
        title: $t('settings.workflow.tableRelated'),
        minWidth: 200,
        editRender: { name: 'VxeInput' },
      },
      {
        field: 'STATUSFIELD',
        title: $t('settings.workflow.statusField'),
        minWidth: 150,
        editRender: { name: 'VxeInput' },
      },
      {
        field: 'DISSTATUSFIELD',
        title: $t('settings.workflow.dispStatusField'),
        minWidth: 150,
        editRender: { name: 'VxeInput' },
      },
      {
        field: 'REJECTFIELD',
        title: $t('settings.workflow.rejectField'),
        minWidth: 150,
        editRender: { name: 'VxeInput' },
      },
      {
        align: 'center',
        cellRender: {
          attrs: {
            nameField: 'name',
            nameTitle: $t('settings.workflow.title'),
            onClick: onActionClick,
          },
          name: 'CellOperation',
          options: [
            {
              code: 'edit',
            },
            {
              code: 'delete',
            },
          ],
        },
        field: 'operation',
        fixed: 'right',
        headerAlign: 'center',
        showOverflow: false,
        title: $t('settings.workflow.operation'),
        width: 100,
      },
    ],
    height: 'auto',
    pagerConfig: {
      enabled: false,
    },
    proxyConfig: {
      ajax: {
        query: async (_params) => {
          return await getWorkflows();
        },
      },
    },
    toolbarConfig: {
      slots: {
        buttons: 'toolbarButtons',
      },
      custom: true,
      export: false,
      refresh: { code: 'query' },
      zoom: true,
    },
    columnConfig: {
      resizable: true,
    },
    rowConfig: {
      isCurrent: true,
      keyField: 'ORIGREC',
    },
    editConfig: {
      trigger: 'click',
      mode: 'cell',
    },
    menuConfig: {
      body: {
        options: [
          [
            {
              code: 'viewAudit',
              name: '查看历史',
              prefixConfig: { icon: 'vxe-icon-table' },
              visible: true,
              disabled: false,
            },
          ],
        ],
      },
    },
    keepSource: true,
    params: {
      limsControlId: 'WorkFlow_gd',
      tableName: 'GENERAL_WORKFLOWS',
    },
  } as VxeTableGridOptions,
});

function onActionClick({ code, row }: OnActionClickParams) {
  switch (code) {
    case 'delete': {
      onDelete(row);
      break;
    }
    case 'edit': {
      onEdit(row);
      break;
    }
  }
}

async function onDelete(row: Recordable<any>) {
  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting', [row.BUILDING_NAME]),
    duration: 0,
    key: 'action_process_msg',
  });

  callServer('Areas.DeleteArea', [row.ORIGREC])
    .then((msg) => {
      if (msg) {
        message.error({
          content: $t(`static-tables.areas.${msg}`, [row.AREA_NAME]),
          key: 'action_process_msg',
        });
        return;
      }

      message.success({
        content: $t('ui.actionMessage.deleteSuccess', [row.BUILDING_NAME]),
        key: 'action_process_msg',
      });

      workflowGridApi?.query();
    })
    .catch(() => {
      hideLoading();
    });
}

function onCreate() {
  formModalApi.setData(null).open();
}

function onEdit(row: Recordable<any>) {
  flowModalApi.setData(row).open();
}
</script>

<template>
  <Page auto-content-height>
    <FormModal
      :title="$t('ui.actionTitle.create', [$t('settings.workflow.workflow')])"
    >
      <Form class="mx-4" />
      <template #prepend-footer>
        <Space class="flex-auto">
          <Button type="primary" danger @click="resetForm">
            {{ $t('common.reset') }}
          </Button>
        </Space>
      </template>
      <template #center-footer>
        <Space>
          <Button
            type="primary"
            @click="addWorkflowSecondStep"
            v-if="addWorkflowStep === 1"
          >
            {{ $t('common.nextStep') }}
          </Button>
          <Button
            type="primary"
            @click="addWorkflowDone"
            v-if="addWorkflowStep === 2"
          >
            {{ $t('common.done') }}
          </Button>
        </Space>
      </template>
    </FormModal>
    <FlowModal
      :title="$t('ui.actionTitle.edit2', [$t('settings.workflow.workflow')])"
    />
    <WorkflowGrid class="mx-2">
      <template #toolbarButtons>
        <Button type="primary" @click="onCreate">
          <EditIcon class="size-5" />
          {{ $t('ui.actionTitle.create', [$t('settings.workflow.workflow')]) }}
        </Button>
      </template>
    </WorkflowGrid>
  </Page>
</template>
