using System.Text.Json;
using Ardalis.Result;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Witlab.Platform.Core.Platform;
using Witlab.Platform.Core.Platform.ValueObjects;
using Witlab.Platform.Infrastructure.Auth.Interfaces;

namespace Witlab.Platform.Infrastructure.Auth;

/// <summary>
/// 用于缓存序列化的用户会话DTO
/// </summary>
public class UserSessionCacheDto
{
  public Guid Id { get; set; }
  public Guid UserId { get; set; }
  public string UserName { get; set; } = string.Empty;
  public string TokenId { get; set; } = string.Empty;
  public DateTime LoginTime { get; set; }
  public DateTime LastActivityTime { get; set; }
  public DateTime TokenExpiresAt { get; set; }
  public SessionInfo SessionInfo { get; set; } = new SessionInfo();
  public SessionStatus Status { get; set; }
  public LoginSource LoginSource { get; set; }

  public bool IsActive => Status == SessionStatus.Active && DateTime.UtcNow < TokenExpiresAt;
  public bool IsExpired => DateTime.UtcNow >= TokenExpiresAt;

  public static UserSessionCacheDto FromDomain(UserSession session)
  {
    return new UserSessionCacheDto
    {
      Id = session.Id,
      UserId = session.UserId,
      UserName = session.UserName,
      TokenId = session.TokenId,
      LoginTime = session.LoginTime,
      LastActivityTime = session.LastActivityTime,
      TokenExpiresAt = session.TokenExpiresAt,
      SessionInfo = session.SessionInfo,
      Status = session.Status,
      LoginSource = session.LoginSource
    };
  }

  public UserSession ToDomain()
  {
    return UserSession.FromCache(
      Id,
      UserId,
      UserName,
      TokenId,
      LoginTime,
      LastActivityTime,
      TokenExpiresAt,
      SessionInfo,
      Status,
      LoginSource);
  }
}

/// <summary>
/// 基于缓存的用户会话服务实现
/// </summary>
public class UserSessionService : IUserSessionService
{
  private readonly IDistributedCache _cache;
  private readonly ILogger<UserSessionService> _logger;
  private readonly SessionSettings _sessionSettings;

  private const string SESSION_KEY_PREFIX = "user_session_";
  private const string TOKEN_SESSION_KEY_PREFIX = "token_session_";
  private const string USER_SESSIONS_KEY_PREFIX = "user_sessions_";
  private const string ACTIVE_SESSIONS_KEY = "active_sessions";

  public UserSessionService(
    IDistributedCache cache,
    ILogger<UserSessionService> logger,
    IOptions<SessionSettings> sessionSettings)
  {
    _cache = cache;
    _logger = logger;
    _sessionSettings = sessionSettings.Value;
  }

  /// <inheritdoc />
  public async Task<Result<UserSession>> CreateSessionAsync(
    Guid userId,
    string userName,
    string tokenId,
    DateTime tokenExpiresAt,
    SessionInfo sessionInfo,
    LoginSource loginSource = LoginSource.Web)
  {
    try
    {
      _logger.LogDebug("开始为用户 {UserId} ({UserName}) 创建会话，TokenId: {TokenId}", 
        userId, userName, tokenId);

      // 检查并发会话限制
      if (_sessionSettings.MaxConcurrentSessions > 0)
      {
        var limitCheckResult = await CheckConcurrentSessionLimitAsync(userId, _sessionSettings.MaxConcurrentSessions);
        if (limitCheckResult.IsSuccess && limitCheckResult.Value)
        {
          _logger.LogWarning("用户 {UserId} 超出并发会话限制 {MaxSessions}", 
            userId, _sessionSettings.MaxConcurrentSessions);
          
          if (!_sessionSettings.AllowMultipleDeviceLogin)
          {
            // 强制下线旧会话
            await ForceTerminateAllUserSessionsAsync(userId, "新会话登录，旧会话被强制下线");
          }
          else
          {
            return Result<UserSession>.Error("超出最大并发会话数限制");
          }
        }
      }

      // 创建会话
      var session = new UserSession(userId, userName, tokenId, tokenExpiresAt, sessionInfo, loginSource);
      var cacheDto = UserSessionCacheDto.FromDomain(session);

      // 计算缓存过期时间
      var timeToLive = tokenExpiresAt - DateTime.UtcNow;
      if (timeToLive <= TimeSpan.Zero)
      {
        return Result<UserSession>.Error("Token过期时间必须在未来");
      }

      // 存储会话数据
      await StoreSessionAsync(cacheDto, timeToLive);

      _logger.LogInformation("成功为用户 {UserId} ({UserName}) 创建会话 {SessionId}，TokenId: {TokenId}",
        userId, userName, session.Id, tokenId);

      return Result<UserSession>.Success(session);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "为用户 {UserId} ({UserName}) 创建会话时发生错误", userId, userName);
      return Result<UserSession>.Error("创建会话失败");
    }
  }

  /// <inheritdoc />
  public async Task<Result<UserSession?>> GetSessionByTokenIdAsync(string tokenId)
  {
    try
    {
      var cacheKey = $"{TOKEN_SESSION_KEY_PREFIX}{tokenId}";
      var jsonData = await _cache.GetStringAsync(cacheKey);

      if (string.IsNullOrEmpty(jsonData))
      {
        return Result<UserSession?>.Success(null);
      }

      var cacheDto = JsonSerializer.Deserialize<UserSessionCacheDto>(jsonData);
      if (cacheDto == null)
      {
        return Result<UserSession?>.Success(null);
      }

      // 检查会话是否过期
      if (cacheDto.IsExpired && cacheDto.Status == SessionStatus.Active)
      {
        cacheDto.Status = SessionStatus.Expired;
        await UpdateSessionInCacheAsync(cacheDto);
      }

      return Result<UserSession?>.Success(cacheDto.ToDomain());
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "根据TokenId {TokenId} 获取会话时发生错误", tokenId);
      return Result<UserSession?>.Error("获取会话失败");
    }
  }

  /// <inheritdoc />
  public async Task<Result<UserSession?>> GetSessionByIdAsync(Guid sessionId)
  {
    try
    {
      var cacheKey = $"{SESSION_KEY_PREFIX}{sessionId}";
      var jsonData = await _cache.GetStringAsync(cacheKey);

      if (string.IsNullOrEmpty(jsonData))
      {
        return Result<UserSession?>.Success(null);
      }

      var cacheDto = JsonSerializer.Deserialize<UserSessionCacheDto>(jsonData);
      if (cacheDto == null)
      {
        return Result<UserSession?>.Success(null);
      }

      // 检查会话是否过期
      if (cacheDto.IsExpired && cacheDto.Status == SessionStatus.Active)
      {
        cacheDto.Status = SessionStatus.Expired;
        await UpdateSessionInCacheAsync(cacheDto);
      }

      return Result<UserSession?>.Success(cacheDto.ToDomain());
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "根据SessionId {SessionId} 获取会话时发生错误", sessionId);
      return Result<UserSession?>.Error("获取会话失败");
    }
  }

  /// <summary>
  /// 存储会话到缓存
  /// </summary>
  private async Task StoreSessionAsync(UserSessionCacheDto cacheDto, TimeSpan timeToLive)
  {
    var jsonData = JsonSerializer.Serialize(cacheDto);
    var cacheOptions = new DistributedCacheEntryOptions
    {
      AbsoluteExpirationRelativeToNow = timeToLive
    };

    // 存储会话数据（按会话ID）
    var sessionKey = $"{SESSION_KEY_PREFIX}{cacheDto.Id}";
    await _cache.SetStringAsync(sessionKey, jsonData, cacheOptions);

    // 存储Token到会话的映射
    var tokenKey = $"{TOKEN_SESSION_KEY_PREFIX}{cacheDto.TokenId}";
    await _cache.SetStringAsync(tokenKey, jsonData, cacheOptions);

    // 添加到用户会话列表
    await AddToUserSessionListAsync(cacheDto.UserId, cacheDto.Id, timeToLive);

    // 添加到活跃会话列表
    await AddToActiveSessionListAsync(cacheDto.Id, timeToLive);
  }

  /// <summary>
  /// 更新缓存中的会话数据
  /// </summary>
  private async Task UpdateSessionInCacheAsync(UserSessionCacheDto cacheDto)
  {
    var timeToLive = cacheDto.TokenExpiresAt - DateTime.UtcNow;
    if (timeToLive > TimeSpan.Zero)
    {
      await StoreSessionAsync(cacheDto, timeToLive);
    }
    else
    {
      // 会话已过期，删除缓存
      await RemoveSessionFromCacheAsync(cacheDto);
    }
  }

  /// <summary>
  /// 从缓存中删除会话
  /// </summary>
  private async Task RemoveSessionFromCacheAsync(UserSessionCacheDto cacheDto)
  {
    var sessionKey = $"{SESSION_KEY_PREFIX}{cacheDto.Id}";
    var tokenKey = $"{TOKEN_SESSION_KEY_PREFIX}{cacheDto.TokenId}";

    await _cache.RemoveAsync(sessionKey);
    await _cache.RemoveAsync(tokenKey);
    await RemoveFromUserSessionListAsync(cacheDto.UserId, cacheDto.Id);
    await RemoveFromActiveSessionListAsync(cacheDto.Id);
  }

  /// <summary>
  /// 添加到用户会话列表
  /// </summary>
  private async Task AddToUserSessionListAsync(Guid userId, Guid sessionId, TimeSpan timeToLive)
  {
    try
    {
      var userSessionsKey = $"{USER_SESSIONS_KEY_PREFIX}{userId}";
      var sessionsJson = await _cache.GetStringAsync(userSessionsKey);

      var sessionList = string.IsNullOrEmpty(sessionsJson)
        ? new List<Guid>()
        : JsonSerializer.Deserialize<List<Guid>>(sessionsJson) ?? new List<Guid>();

      if (!sessionList.Contains(sessionId))
      {
        sessionList.Add(sessionId);
        var updatedJson = JsonSerializer.Serialize(sessionList);

        await _cache.SetStringAsync(userSessionsKey, updatedJson, new DistributedCacheEntryOptions
        {
          AbsoluteExpirationRelativeToNow = timeToLive.Add(TimeSpan.FromMinutes(5))
        });
      }
    }
    catch (Exception ex)
    {
      _logger.LogWarning(ex, "添加会话 {SessionId} 到用户 {UserId} 会话列表时发生错误", sessionId, userId);
    }
  }

  /// <summary>
  /// 从用户会话列表中移除
  /// </summary>
  private async Task RemoveFromUserSessionListAsync(Guid userId, Guid sessionId)
  {
    try
    {
      var userSessionsKey = $"{USER_SESSIONS_KEY_PREFIX}{userId}";
      var sessionsJson = await _cache.GetStringAsync(userSessionsKey);

      if (string.IsNullOrEmpty(sessionsJson))
        return;

      var sessionList = JsonSerializer.Deserialize<List<Guid>>(sessionsJson);
      if (sessionList != null && sessionList.Remove(sessionId))
      {
        if (sessionList.Any())
        {
          var updatedJson = JsonSerializer.Serialize(sessionList);
          await _cache.SetStringAsync(userSessionsKey, updatedJson, new DistributedCacheEntryOptions
          {
            AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(24)
          });
        }
        else
        {
          await _cache.RemoveAsync(userSessionsKey);
        }
      }
    }
    catch (Exception ex)
    {
      _logger.LogWarning(ex, "从用户 {UserId} 会话列表中移除会话 {SessionId} 时发生错误", userId, sessionId);
    }
  }

  /// <summary>
  /// 添加到活跃会话列表
  /// </summary>
  private async Task AddToActiveSessionListAsync(Guid sessionId, TimeSpan timeToLive)
  {
    try
    {
      var activeSessionsJson = await _cache.GetStringAsync(ACTIVE_SESSIONS_KEY);
      var sessionList = string.IsNullOrEmpty(activeSessionsJson)
        ? new List<Guid>()
        : JsonSerializer.Deserialize<List<Guid>>(activeSessionsJson) ?? new List<Guid>();

      if (!sessionList.Contains(sessionId))
      {
        sessionList.Add(sessionId);
        var updatedJson = JsonSerializer.Serialize(sessionList);

        await _cache.SetStringAsync(ACTIVE_SESSIONS_KEY, updatedJson, new DistributedCacheEntryOptions
        {
          AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(24)
        });
      }
    }
    catch (Exception ex)
    {
      _logger.LogWarning(ex, "添加会话 {SessionId} 到活跃会话列表时发生错误", sessionId);
    }
  }

  /// <summary>
  /// 从活跃会话列表中移除
  /// </summary>
  private async Task RemoveFromActiveSessionListAsync(Guid sessionId)
  {
    try
    {
      var activeSessionsJson = await _cache.GetStringAsync(ACTIVE_SESSIONS_KEY);
      if (string.IsNullOrEmpty(activeSessionsJson))
        return;

      var sessionList = JsonSerializer.Deserialize<List<Guid>>(activeSessionsJson);
      if (sessionList != null && sessionList.Remove(sessionId))
      {
        var updatedJson = JsonSerializer.Serialize(sessionList);
        await _cache.SetStringAsync(ACTIVE_SESSIONS_KEY, updatedJson, new DistributedCacheEntryOptions
        {
          AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(24)
        });
      }
    }
    catch (Exception ex)
    {
      _logger.LogWarning(ex, "从活跃会话列表中移除会话 {SessionId} 时发生错误", sessionId);
    }
  }

  /// <inheritdoc />
  public async Task<Result<List<UserSession>>> GetActiveSessionsByUserIdAsync(Guid userId)
  {
    try
    {
      var userSessionsKey = $"{USER_SESSIONS_KEY_PREFIX}{userId}";
      var sessionsJson = await _cache.GetStringAsync(userSessionsKey);

      if (string.IsNullOrEmpty(sessionsJson))
      {
        return Result<List<UserSession>>.Success(new List<UserSession>());
      }

      var sessionIds = JsonSerializer.Deserialize<List<Guid>>(sessionsJson);
      if (sessionIds == null || !sessionIds.Any())
      {
        return Result<List<UserSession>>.Success(new List<UserSession>());
      }

      var activeSessions = new List<UserSession>();
      foreach (var sessionId in sessionIds)
      {
        var sessionResult = await GetSessionByIdAsync(sessionId);
        if (sessionResult.IsSuccess && sessionResult.Value != null && sessionResult.Value.IsActive)
        {
          activeSessions.Add(sessionResult.Value);
        }
      }

      return Result<List<UserSession>>.Success(activeSessions);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取用户 {UserId} 的活跃会话时发生错误", userId);
      return Result<List<UserSession>>.Error("获取用户活跃会话失败");
    }
  }

  /// <inheritdoc />
  public async Task<Result<(List<UserSession> Sessions, int TotalCount)>> GetActiveSessionsAsync(int pageNumber = 1, int pageSize = 20)
  {
    try
    {
      var activeSessionsJson = await _cache.GetStringAsync(ACTIVE_SESSIONS_KEY);
      if (string.IsNullOrEmpty(activeSessionsJson))
      {
        return Result<(List<UserSession>, int)>.Success((new List<UserSession>(), 0));
      }

      var sessionIds = JsonSerializer.Deserialize<List<Guid>>(activeSessionsJson);
      if (sessionIds == null || !sessionIds.Any())
      {
        return Result<(List<UserSession>, int)>.Success((new List<UserSession>(), 0));
      }

      var activeSessions = new List<UserSession>();
      foreach (var sessionId in sessionIds)
      {
        var sessionResult = await GetSessionByIdAsync(sessionId);
        if (sessionResult.IsSuccess && sessionResult.Value != null && sessionResult.Value.IsActive)
        {
          activeSessions.Add(sessionResult.Value);
        }
      }

      // 按最后活动时间降序排序
      activeSessions = activeSessions.OrderByDescending(s => s.LastActivityTime).ToList();

      var totalCount = activeSessions.Count;
      var pagedSessions = activeSessions
        .Skip((pageNumber - 1) * pageSize)
        .Take(pageSize)
        .ToList();

      return Result<(List<UserSession>, int)>.Success((pagedSessions, totalCount));
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取活跃会话列表时发生错误");
      return Result<(List<UserSession>, int)>.Error("获取活跃会话列表失败");
    }
  }

  /// <inheritdoc />
  public async Task<Result> UpdateSessionActivityAsync(string tokenId)
  {
    try
    {
      var sessionResult = await GetSessionByTokenIdAsync(tokenId);
      if (!sessionResult.IsSuccess || sessionResult.Value == null)
      {
        return Result.Error("会话不存在");
      }

      var session = sessionResult.Value;
      if (!session.IsActive)
      {
        return Result.Error("会话已失效");
      }

      session.UpdateActivity();

      var cacheDto = UserSessionCacheDto.FromDomain(session);
      await UpdateSessionInCacheAsync(cacheDto);

      _logger.LogDebug("更新会话 {SessionId} 活动时间成功", session.Id);
      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "更新会话活动时间时发生错误，TokenId: {TokenId}", tokenId);
      return Result.Error("更新会话活动时间失败");
    }
  }

  /// <inheritdoc />
  public async Task<Result> TerminateSessionAsync(string tokenId)
  {
    try
    {
      var sessionResult = await GetSessionByTokenIdAsync(tokenId);
      if (!sessionResult.IsSuccess || sessionResult.Value == null)
      {
        return Result.Error("会话不存在");
      }

      var session = sessionResult.Value;
      session.Logout();

      var cacheDto = UserSessionCacheDto.FromDomain(session);
      await RemoveSessionFromCacheAsync(cacheDto);

      _logger.LogInformation("用户 {UserId} ({UserName}) 主动注销会话 {SessionId}",
        session.UserId, session.UserName, session.Id);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "终止会话时发生错误，TokenId: {TokenId}", tokenId);
      return Result.Error("终止会话失败");
    }
  }

  /// <inheritdoc />
  public async Task<Result> ForceTerminateSessionAsync(Guid sessionId, string? reason = null)
  {
    try
    {
      var sessionResult = await GetSessionByIdAsync(sessionId);
      if (!sessionResult.IsSuccess || sessionResult.Value == null)
      {
        return Result.Error("会话不存在");
      }

      var session = sessionResult.Value;
      session.ForceLogout(reason);

      var cacheDto = UserSessionCacheDto.FromDomain(session);
      await RemoveSessionFromCacheAsync(cacheDto);

      _logger.LogInformation("管理员强制下线用户 {UserId} ({UserName}) 的会话 {SessionId}，原因: {Reason}",
        session.UserId, session.UserName, session.Id, reason ?? "未指定");

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "强制终止会话时发生错误，SessionId: {SessionId}", sessionId);
      return Result.Error("强制终止会话失败");
    }
  }

  /// <inheritdoc />
  public async Task<Result> ForceTerminateAllUserSessionsAsync(Guid userId, string? reason = null)
  {
    try
    {
      var userSessionsResult = await GetActiveSessionsByUserIdAsync(userId);
      if (!userSessionsResult.IsSuccess)
      {
        return Result.Error("获取用户会话失败");
      }

      var sessions = userSessionsResult.Value;
      var terminatedCount = 0;

      foreach (var session in sessions)
      {
        var result = await ForceTerminateSessionAsync(session.Id, reason);
        if (result.IsSuccess)
        {
          terminatedCount++;
        }
      }

      _logger.LogInformation("强制下线用户 {UserId} 的所有会话，共 {TotalSessions} 个，成功 {TerminatedCount} 个",
        userId, sessions.Count, terminatedCount);

      return terminatedCount > 0 ? Result.Success() : Result.Error("没有会话被终止");
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "强制终止用户所有会话时发生错误，UserId: {UserId}", userId);
      return Result.Error("强制终止用户所有会话失败");
    }
  }

  /// <inheritdoc />
  public async Task<Result<int>> CleanupExpiredSessionsAsync()
  {
    try
    {
      var activeSessionsJson = await _cache.GetStringAsync(ACTIVE_SESSIONS_KEY);
      if (string.IsNullOrEmpty(activeSessionsJson))
      {
        return Result<int>.Success(0);
      }

      var sessionIds = JsonSerializer.Deserialize<List<Guid>>(activeSessionsJson);
      if (sessionIds == null || !sessionIds.Any())
      {
        return Result<int>.Success(0);
      }

      var cleanedCount = 0;
      var validSessionIds = new List<Guid>();

      foreach (var sessionId in sessionIds)
      {
        var sessionResult = await GetSessionByIdAsync(sessionId);
        if (sessionResult.IsSuccess && sessionResult.Value != null)
        {
          var session = sessionResult.Value;
          if (session.IsExpired && session.Status == SessionStatus.Active)
          {
            session.MarkAsExpired();
            var cacheDto = UserSessionCacheDto.FromDomain(session);
            await RemoveSessionFromCacheAsync(cacheDto);
            cleanedCount++;
          }
          else if (session.IsActive)
          {
            validSessionIds.Add(sessionId);
          }
        }
      }

      // 更新活跃会话列表
      if (cleanedCount > 0)
      {
        var updatedJson = JsonSerializer.Serialize(validSessionIds);
        await _cache.SetStringAsync(ACTIVE_SESSIONS_KEY, updatedJson, new DistributedCacheEntryOptions
        {
          AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(24)
        });
      }

      _logger.LogInformation("清理过期会话完成，共清理 {CleanedCount} 个会话", cleanedCount);
      return Result<int>.Success(cleanedCount);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "清理过期会话时发生错误");
      return Result<int>.Error("清理过期会话失败");
    }
  }

  /// <inheritdoc />
  public async Task<Result<SessionStatistics>> GetSessionStatisticsAsync()
  {
    try
    {
      var activeSessionsResult = await GetActiveSessionsAsync(1, int.MaxValue);
      if (!activeSessionsResult.IsSuccess)
      {
        return Result<SessionStatistics>.Error("获取活跃会话失败");
      }

      var sessions = activeSessionsResult.Value.Sessions;
      var today = DateTime.UtcNow.Date;

      var statistics = new SessionStatistics
      {
        TotalActiveSessions = sessions.Count,
        TotalOnlineUsers = sessions.Select(s => s.UserId).Distinct().Count(),
        WebSessions = sessions.Count(s => s.LoginSource == LoginSource.Web),
        MobileSessions = sessions.Count(s => s.LoginSource == LoginSource.Mobile),
        DesktopSessions = sessions.Count(s => s.LoginSource == LoginSource.Desktop),
        ApiSessions = sessions.Count(s => s.LoginSource == LoginSource.Api),
        TodayNewSessions = sessions.Count(s => s.LoginTime.Date == today),
        StatisticsTime = DateTime.UtcNow
      };

      return Result<SessionStatistics>.Success(statistics);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取会话统计信息时发生错误");
      return Result<SessionStatistics>.Error("获取会话统计信息失败");
    }
  }

  /// <inheritdoc />
  public async Task<Result<bool>> CheckConcurrentSessionLimitAsync(Guid userId, int maxConcurrentSessions)
  {
    try
    {
      var userSessionsResult = await GetActiveSessionsByUserIdAsync(userId);
      if (!userSessionsResult.IsSuccess)
      {
        return Result<bool>.Error("获取用户会话失败");
      }

      var activeSessionCount = userSessionsResult.Value.Count;
      var exceedsLimit = activeSessionCount >= maxConcurrentSessions;

      _logger.LogDebug("用户 {UserId} 当前活跃会话数: {ActiveSessions}，限制: {MaxSessions}，是否超限: {ExceedsLimit}",
        userId, activeSessionCount, maxConcurrentSessions, exceedsLimit);

      return Result<bool>.Success(exceedsLimit);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "检查用户并发会话限制时发生错误，UserId: {UserId}", userId);
      return Result<bool>.Error("检查并发会话限制失败");
    }
  }
}
