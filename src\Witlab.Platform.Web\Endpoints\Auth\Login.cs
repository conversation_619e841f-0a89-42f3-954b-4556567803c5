﻿using Witlab.Platform.Infrastructure.Auth.Interfaces;
using Witlab.Platform.Web.Endpoints.Auth.Models;

namespace Witlab.Platform.Web.Endpoints.Auth;

/// <summary>
/// 登录接口
/// </summary>
public class Login(IAuthService _authService) : Endpoint<LoginRequest, LoginResponse>
{
  public override void Configure()
  {
    Post("/auth/login");
    Description(x => x.WithTags("Auth"));
    AllowAnonymous();
    Summary(s =>
    {
      s.Summary = "用户登录";
      s.Description = "验证用户名和密码，返回访问令牌和刷新令牌";
      s.ExampleRequest = new LoginRequest { UserName = "admin", Password = "admin123" };
    });
  }

  public override async Task HandleAsync(LoginRequest request, CancellationToken cancellationToken)
  {
    var result = await _authService.LoginAsync(request.UserName!, request.Password!);

    if (result.IsSuccess)
    {
      var authResponse = result.Value;
      Response = new LoginResponse
      {
        AccessToken = authResponse.AccessToken,
        RefreshToken = authResponse.RefreshToken,
        ExpiresIn = authResponse.ExpiresIn,
        TokenType = authResponse.TokenType,
        UserId = authResponse.UserId,
        UserName = authResponse.UserName
      };
      return;
    }

    foreach (var error in result.Errors)
    {
      AddError(error);
    }

    await SendErrorsAsync();
  }
}
