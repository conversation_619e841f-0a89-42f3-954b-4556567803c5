<script setup lang="ts">
import type {
  ValidationResult,
  WorkflowComponentProps,
  WorkflowData,
  WorkflowEdge,
  WorkflowNode,
} from './types/workflow';

import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue';

import { cloneDeep } from '@vben/utils';

import LogicFlowCore from '@logicflow/core';
import { Menu, MiniMap, Snapshot } from '@logicflow/extension';
import {
  Button,
  ButtonGroup,
  Drawer,
  List,
  ListItem,
  ListItemMeta,
  message,
  Modal,
  Result,
  Space,
  Table,
  Tabs,
  Tag,
} from 'ant-design-vue';
import {
  Expand,
  Eye,
  Image,
  Redo,
  Save,
  Trash2,
  Undo,
  ZoomIn,
  ZoomOut,
} from 'lucide-vue-next';

import WorkflowNodePanel from './components/WorkflowNodePanel.vue';
import WorkflowPropertyPanel from './components/WorkflowPropertyPanel.vue';
import { getNodePanelConfig } from './config/nodes';
import {
  registerAllBaseNodes,
  registerAllCustomNodes,
  registerAllVueComponentNodes,
} from './registerNode';
import { validateWorkflow } from './utils/validator';

import '@logicflow/core/dist/index.css';
import '@logicflow/extension/dist/index.css';

// Props
interface Props extends WorkflowComponentProps {}

// Events
interface Events {
  nodeSelect: [node: null | WorkflowNode];
  edgeSelect: [edge: null | WorkflowEdge];
  nodeAdd: [node: WorkflowNode];
  nodeDelete: [nodeId: string];
  edgeAdd: [edge: WorkflowEdge];
  edgeDelete: [edgeId: string];
  nodeUpdate: [node: WorkflowNode];
  edgeUpdate: [edge: WorkflowEdge];
  validate: [result: ValidationResult];
  'update:flowData': [data: WorkflowData];
  save: [
    data: {
      deletedEdges: WorkflowEdge[];
      deletedNodes: WorkflowNode[];
      newEdges: WorkflowEdge[];
      newNodes: WorkflowNode[];
      updatedEdges: WorkflowEdge[];
      updatedNodes: WorkflowNode[];
    },
  ];
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'edit',
  readonly: false,
  config: () => ({
    width: undefined,
    height: undefined,
    background: '#f7f9ff',
    grid: true,
    miniMap: true,
    keyboard: true,
  }),
  flowData: () => ({
    nodes: [],
    edges: [],
  }),
  nodeTemplates: () => [],
  approverDataSource: () => [],
});

const emit = defineEmits<Events>();

// 响应式数据
const canvasContainer = ref<HTMLDivElement>();
const lf = ref<LogicFlowCore>();
const currentFlowData = ref<WorkflowData>(props.flowData);
const canUndo = ref(false);
const canRedo = ref(false);

// 变更跟踪
const originalData = ref<WorkflowData>({ nodes: [], edges: [] });
const deletedNodes = ref<WorkflowNode[]>([]);
const deletedEdges = ref<WorkflowEdge[]>([]);

// UI状态
const propertyDrawerVisible = ref(false);
const showDataModal = ref(false);
const showValidationModal = ref(false);
const selectedElement = ref<null | WorkflowEdge | WorkflowNode>(null);
const selectedElementType = ref<'edge' | 'node' | null>(null);
const validationResult = ref<null | ValidationResult>(null);

// 配置数据
const nodeConfigs = computed(() => getNodePanelConfig());

// 表格列定义
const nodeColumns = [
  { title: 'ID', dataIndex: 'id', key: 'id' },
  { title: '类型', dataIndex: 'type', key: 'type' },
  { title: '名称', dataIndex: ['properties', 'name'], key: 'name' },
  { title: 'X坐标', dataIndex: 'x', key: 'x' },
  { title: 'Y坐标', dataIndex: 'y', key: 'y' },
];

const edgeColumns = [
  { title: 'ID', dataIndex: 'id', key: 'id' },
  { title: '类型', dataIndex: 'type', key: 'type' },
  { title: '源节点', dataIndex: 'sourceNodeId', key: 'sourceNodeId' },
  { title: '目标节点', dataIndex: 'targetNodeId', key: 'targetNodeId' },
];

// 生命周期
onMounted(() => {
  initLogicFlow();
});

// 监听数据变化
watch(
  () => props.flowData,
  (newData) => {
    if (newData && lf.value) {
      currentFlowData.value = newData;
      lf.value.render(newData);
    }
  },
  { deep: true },
);

/**
 * 变更跟踪系统说明：
 *
 * 1. 新增元素：updateFlowData() 自动检测并标记 isNew = true
 * 2. 修改元素：markAsUpdated() 标记 isUpdated = true（仅限非新增元素）
 * 3. 删除元素：markAsDeleted() 记录到 deletedNodes/deletedEdges（仅限非新增元素）
 *
 * 特殊情况处理：
 * - 新增后删除：元素相对于原始数据是"不存在"的，既不在新增列表中，也不在删除列表中
 * - 新增后修改：仍标记为 isNew = true，不会标记为 isUpdated
 * - 原始元素修改后删除：会出现在删除列表中，保留最新的修改状态
 */

// 数据变更跟踪方法
const initializeOriginalData = () => {
  originalData.value = cloneDeep(currentFlowData.value);
};

// markAsNew 函数已不再需要，新增操作由 updateFlowData() 自动处理

const markAsUpdated = (id: string, type: 'edge' | 'node') => {
  if (type === 'node') {
    const node = currentFlowData.value.nodes.find((n) => n.id === id);
    if (node && !node.isNew) {
      node.isUpdated = true;
    }
    return node as WorkflowNode;
  } else {
    const edge = currentFlowData.value.edges.find((e) => e.id === id);
    if (edge && !edge.isNew) {
      edge.isUpdated = true;
    }
    return edge as WorkflowEdge;
  }
};

const markAsDeleted = (id: string, type: 'edge' | 'node') => {
  if (type === 'node') {
    const node = currentFlowData.value.nodes.find((n) => n.id === id);
    // 只有原始数据中存在的节点（非新增）才需要记录删除
    // 新增后又删除的节点相对于原始数据是"不存在"的，不需要记录
    if (node && !node.isNew) {
      node.isDeleted = true;
      deletedNodes.value.push({ ...node });
    }
  } else {
    const edge = currentFlowData.value.edges.find((e) => e.id === id);
    // 只有原始数据中存在的边（非新增）才需要记录删除
    // 新增后又删除的边相对于原始数据是"不存在"的，不需要记录
    if (edge && !edge.isNew) {
      edge.isDeleted = true;
      deletedEdges.value.push({ ...edge });
    }
  }
};

// 方法
const initLogicFlow = async () => {
  if (!canvasContainer.value) return;

  await nextTick();

  const lfInstance = new LogicFlowCore({
    container: canvasContainer.value,
    width: props.config?.width,
    height: props.config?.height,
    background: {
      backgroundColor: props.config?.background || '#f7f9ff',
    },
    grid: {
      size: 10,
      visible: props.config?.grid ?? true,
    },
    keyboard: {
      enabled: props.config?.keyboard ?? true,
    },
    plugins: [Menu, MiniMap, Snapshot],
    edgeTextDraggable: true,
    hoverOutline: false,
    // 只读模式配置
    isSilentMode: props.readonly,
    stopScrollGraph: props.readonly,
    stopZoomGraph: props.readonly,
    stopMoveGraph: props.readonly,
  });

  lf.value = lfInstance;

  // 注册自定义节点
  registerCustomNodes();

  // 设置主题
  setTheme();

  // 绑定事件
  bindEvents();

  // 渲染初始数据
  if (currentFlowData.value) {
    lfInstance.render(currentFlowData.value);
  }

  // 初始化原始数据用于变更跟踪
  initializeOriginalData();
};

const registerCustomNodes = () => {
  if (!lf.value) return;

  registerAllCustomNodes(lf.value);
  registerAllBaseNodes(lf.value);
  registerAllVueComponentNodes(lf.value);
};

const setTheme = () => {
  if (!lf.value) return;

  lf.value.setTheme({
    circle: {
      stroke: '#000000',
      strokeWidth: 1,
      outlineColor: '#88f',
    },
    rect: {
      outlineColor: '#88f',
      strokeWidth: 1,
    },
    polygon: {
      strokeWidth: 1,
      outlineColor: '#88f',
    },
    polyline: {
      stroke: '#000000',
      hoverStroke: '#000000',
      selectedStroke: '#000000',
      outlineColor: '#88f',
      strokeWidth: 1,
    },
    nodeText: {
      color: '#000000',
      fontSize: 12,
    },
    edgeText: {
      color: '#000000',
      fontSize: 12,
      textWidth: 100,
      background: {
        fill: '#f7f9ff',
      },
    },
  });
};

// 新增节点的弹窗状态
const showAddNodeModal = ref(false);
const sourceNodeForAdd = ref<any>(null);

// 添加新节点并连接
const addNodeAndConnect = (nodeType: string) => {
  if (!lf.value || !sourceNodeForAdd.value) return;

  const sourceNode = sourceNodeForAdd.value;
  const newNodeX = sourceNode.x + 200;
  const newNodeY = sourceNode.y;

  // 添加新节点
  const newNode = lf.value.addNode({
    type: nodeType,
    x: newNodeX,
    y: newNodeY,
    text: {
      value: '新节点',
      x: newNodeX,
      y: newNodeY + 50,
    },
  });

  // 连接原节点和新节点
  lf.value.addEdge({
    sourceNodeId: sourceNode.id,
    targetNodeId: newNode.id,
    type: 'polyline',
  });

  updateFlowData();

  showAddNodeModal.value = false;
  message.success('节点添加成功');
};

// 设置右键菜单
const setupContextMenu = () => {
  if (!lf.value || !lf.value.extension?.menu) return;

  try {
    // @ts-ignore - LogicFlow 类型定义不完整，但方法确实存在
    lf.value.extension.menu.setMenuConfig({
      nodeMenu: [
        {
          text: '新增节点',
          callback(node: any) {
            contextMenuData.value = node;
            handleContextMenuAction('addNode');
          },
        },
        {
          text: '删除节点',
          callback(node: any) {
            contextMenuData.value = node;
            handleContextMenuAction('delete');
          },
        },
      ],
      edgeMenu: false,
    });
  } catch (error) {
    console.warn('设置右键菜单失败:', error);
  }

  // 使用LogicFlow的原生右键菜单事件
  // lf.value.on('node:contextmenu', ({ data, e }) => {
  //   e.preventDefault();

  //   // 创建自定义菜单
  //   const menuItems = [
  //     { text: '编辑', action: 'edit' },
  //     { text: '删除', action: 'delete' },
  //     { text: '新增节点', action: 'addNode' },
  //   ];

  //   // 这里可以显示自定义的右键菜单
  //   // showCustomContextMenu(e, menuItems, data);
  // });

  // lf.value.on('edge:contextmenu', ({ data, e }) => {
  //   e.preventDefault();

  //   const menuItems = [
  //     { text: '编辑', action: 'edit' },
  //     { text: '删除', action: 'delete' },
  //   ];

  //   // showCustomContextMenu(e, menuItems, data);
  // });
};

// 右键菜单状态
const contextMenuVisible = ref(false);
const contextMenuPosition = ref({ x: 0, y: 0 });
const contextMenuData = ref<any>(null);
const contextMenuType = ref<'edge' | 'node'>('node');

// 执行右键菜单操作
const handleContextMenuAction = (action: string) => {
  const data = contextMenuData.value;

  switch (action) {
    case 'addNode': {
      // showAddNodeMenu(data);
      sourceNodeForAdd.value = data;
      addNodeAndConnect('base-rect');
      break;
    }
    case 'delete': {
      if (contextMenuType.value === 'edge') {
        lf.value?.deleteEdge(data.id);
      } else {
        lf.value?.deleteNode(data.id);
      }
      updateFlowData();
      break;
    }
    case 'edit': {
      selectedElement.value = data;
      selectedElementType.value = contextMenuType.value;
      propertyDrawerVisible.value = true;
      break;
    }
  }

  contextMenuVisible.value = false;
};

// 隐藏右键菜单
const hideContextMenu = () => {
  contextMenuVisible.value = false;
};

const bindEvents = () => {
  if (!lf.value) return;

  // 设置右键菜单
  setupContextMenu();

  // 节点点击事件
  lf.value.on('node:click', ({ data }) => {
    selectedElement.value = data as WorkflowNode;
    data._data = currentFlowData.value.nodes.find(
      (node) => node.id === data.id,
    )?._data;
    selectedElementType.value = 'node';
    propertyDrawerVisible.value = true;
    emit('nodeSelect', data as WorkflowNode);
  });

  // 连线点击事件
  lf.value.on('edge:click', ({ data }) => {
    selectedElement.value = data as WorkflowEdge;
    data._data = currentFlowData.value.edges.find(
      (edge) => edge.id === data.id,
    )?._data;
    selectedElementType.value = 'edge';
    propertyDrawerVisible.value = true;
    emit('edgeSelect', data as WorkflowEdge);
  });

  // 空白区域点击事件
  lf.value.on('blank:click', () => {
    selectedElement.value = null;
    selectedElementType.value = null;
    propertyDrawerVisible.value = false;
    emit('nodeSelect', null);
    emit('edgeSelect', null);
  });

  // 节点添加事件
  lf.value.on('node:add', ({ data }) => {
    // 新增：先更新数据（自动标记为新增），再触发事件
    updateFlowData();
    emit('nodeAdd', data as WorkflowNode);
  });

  lf.value.on('node:dnd-add', ({ data }) => {
    // 新增：先更新数据（自动标记为新增），再触发事件
    updateFlowData();
    emit('nodeAdd', data as WorkflowNode);
  });

  // 节点删除事件
  lf.value.on('node:delete', ({ data }) => {
    // 删除：标记删除并保留在currentFlowData中
    const nodeIndex = currentFlowData.value.nodes.findIndex(
      (n) => n.id === data.id,
    );
    if (nodeIndex !== -1) {
      const node = currentFlowData.value.nodes[nodeIndex];

      // 如果是原始节点（非新增），记录到deletedNodes
      if (node.isNew) {
        // 如果是新增节点，直接从currentFlowData中移除
        currentFlowData.value.nodes.splice(nodeIndex, 1);
      } else {
        markAsDeleted(data.id, 'node');
      }
    }

    emit('nodeDelete', data.id);
    emit('update:flowData', currentFlowData.value);
  });

  // 连线添加事件
  lf.value.on('edge:add', ({ data }) => {
    // 新增：先更新数据（自动标记为新增），再触发事件
    updateFlowData();
    emit('edgeAdd', data as WorkflowEdge);
  });

  // 连线删除事件
  lf.value.on('edge:delete', ({ data }) => {
    // 删除：标记删除并保留在currentFlowData中
    const edgeIndex = currentFlowData.value.edges.findIndex(
      (e) => e.id === data.id,
    );
    if (edgeIndex !== -1) {
      const edge = currentFlowData.value.edges[edgeIndex];

      // 如果是原始边（非新增），记录到deletedEdges
      if (edge.isNew) {
        // 如果是新增边，直接从currentFlowData中移除
        currentFlowData.value.edges.splice(edgeIndex, 1);
      } else {
        markAsDeleted(data.id, 'edge');
        edge.isDeleted = true;
        console.warn(
          `连线 ${edge.id} 已标记为删除，但保留在 currentFlowData 中`,
        );
      }
    }

    emit('edgeDelete', data.id);
    emit('update:flowData', currentFlowData.value);
  });

  lf.value.on('node:properties-change', ({ id }) => {
    // 更新：先更新数据，再标记为已更新
    updateFlowData();
    markAsUpdated(id, 'node');
  });

  // 历史记录变化事件
  lf.value.on('history:change', ({ data: { undoAble, redoAble } }) => {
    canUndo.value = undoAble;
    canRedo.value = redoAble;
  });

  // 连接验证失败事件
  lf.value.on('connection:not-allowed', (data) => {
    message.error(data.msg);
  });

  lf.value.on('miniMap:close', () => {
    miniMapVisible.value = false;
    toggleMiniMap();
  });

  // 全局点击事件，隐藏右键菜单
  document.addEventListener('click', hideContextMenu);
};

// 组件卸载时清理事件监听器
onUnmounted(() => {
  document.removeEventListener('click', hideContextMenu);
});

// 工具栏方法
const undo = () => {
  lf.value?.undo();
};

const redo = () => {
  lf.value?.redo();
};

const zoomIn = () => {
  lf.value?.zoom(true);
};

const zoomOut = () => {
  lf.value?.zoom(false);
};

const fitView = () => {
  lf.value?.fitView();
};

const miniMapVisible = ref(false);
const toggleMiniMap = () => {
  if (lf.value?.extension?.miniMap) {
    const miniMap = lf.value.extension.miniMap as MiniMap;
    miniMapVisible.value = !miniMapVisible.value;
    if (miniMapVisible.value) {
      miniMap.show(lf.value.graphModel.width - 150, 40);
    } else {
      miniMap.hide();
    }
  }
};

// const validateFlow = () => {
//   validationResult.value = validateWorkflow(currentFlowData.value);
//   showValidationModal.value = true;
//   emit('validate', validationResult.value);
// };

// const exportData = () => {
//   const dataStr = JSON.stringify(currentFlowData.value, null, 2);
//   const blob = new Blob([dataStr], { type: 'application/json' });
//   const url = URL.createObjectURL(blob);
//   const link = document.createElement('a');
//   link.href = url;
//   link.download = 'workflow.json';
//   link.click();
//   URL.revokeObjectURL(url);
// };

const clearCanvas = () => {
  if (lf.value) {
    lf.value.clearData();
    updateFlowData();
  }
};

// 数据更新方法
const updateFlowData = () => {
  if (lf.value) {
    const graphData = lf.value.getGraphData() as WorkflowData;

    // 保持 _data 字段和变更标记的一致性
    graphData.nodes = graphData.nodes.map((node) => {
      const existingNode = currentFlowData.value.nodes.find(
        (n) => n.id === node.id,
      );

      return {
        ...node,
        _data: existingNode?._data,
        // 保持或设置变更标记
        // 如果是新节点（在原数据中不存在），标记为新增
        isNew: existingNode?.isNew || !existingNode || false,
        isUpdated: existingNode?.isUpdated || false,
        isDeleted: existingNode?.isDeleted || false,
      };
    });

    graphData.edges = graphData.edges.map((edge) => {
      const existingEdge = currentFlowData.value.edges.find(
        (e) => e.id === edge.id,
      );

      return {
        ...edge,
        _data: existingEdge?._data,
        // 保持或设置变更标记
        // 如果是新边（在原数据中不存在），标记为新增
        isNew: existingEdge?.isNew || !existingEdge || false,
        isUpdated: existingEdge?.isUpdated || false,
        isDeleted: existingEdge?.isDeleted || false,
      };
    });

    currentFlowData.value = graphData;
    emit('update:flowData', currentFlowData.value);
  }
};

// 拖拽节点处理
const handleDragNode = (nodeType: string) => {
  if (lf.value) {
    lf.value.dnd.startDrag({ type: nodeType });
  }
};

// 添加节点到画布中心
const handleAddNode = (
  nodeType: string,
  position?: { x: number; y: number },
) => {
  if (lf.value) {
    const canvasCenter = {
      x: position?.x || lf.value.graphModel.width / 2,
      y: position?.y || lf.value.graphModel.height / 2,
    };

    lf.value.addNode({
      type: nodeType,
      x: canvasCenter.x,
      y: canvasCenter.y,
    });

    updateFlowData();
  }
};

// 元素更新处理
const handleElementUpdate = (updatedElement: WorkflowEdge | WorkflowNode) => {
  if (!lf.value) return;
  if (selectedElementType.value === 'node') {
    const node = updatedElement as WorkflowNode;

    // 更新 LogicFlow 中的 properties
    lf.value.setProperties(node.id, node.properties);
    lf.value.updateText(node.id, node.text?.value ?? '');

    // 更新 currentFlowData 中的节点数据（包括 _data）
    const nodeIndex = currentFlowData.value.nodes.findIndex(
      (n) => n.id === node.id,
    );
    if (nodeIndex !== -1) {
      const updatedNode = markAsUpdated(node.id, 'node') as WorkflowNode;
      currentFlowData.value.nodes[nodeIndex] = {
        ...updatedNode,
        ...node,
      };
      emit('nodeUpdate', updatedNode);
    }
  } else if (selectedElementType.value === 'edge') {
    const edge = updatedElement as WorkflowEdge;

    // 更新 LogicFlow 中的 properties（如果支持）
    // LogicFlow 对边的属性更新支持有限，主要通过重新渲染

    lf.value.updateText(edge.id, edge.text?.value ?? '');

    // 更新 currentFlowData 中的边数据（包括 _data）
    const edgeIndex = currentFlowData.value.edges.findIndex(
      (e) => e.id === edge.id,
    );
    if (edgeIndex !== -1) {
      const updatedEdge = markAsUpdated(edge.id, 'edge') as WorkflowEdge;
      currentFlowData.value.edges[edgeIndex] = {
        ...updatedEdge,
        ...edge,
      };
      emit('edgeUpdate', updatedEdge);
    }
  }

  // 发出数据更新事件
  emit('update:flowData', currentFlowData.value);
};

// 关闭属性面板
const closePropertyDrawer = () => {
  propertyDrawerVisible.value = false;
  selectedElement.value = null;
  selectedElementType.value = null;
};

const handleElementConfirm = (_updatedElement: WorkflowEdge | WorkflowNode) => {
  closePropertyDrawer();
};

// 保存处理
const handleSave = () => {
  console.log('保存按钮被点击，开始处理保存逻辑...');

  const newNodes: WorkflowNode[] = [];
  const updatedNodes: WorkflowNode[] = [];
  const newEdges: WorkflowEdge[] = [];
  const updatedEdges: WorkflowEdge[] = [];

  // 收集新增和修改的节点
  // 注意：只收集当前存在的节点，"新增后删除"的节点不会出现在这里
  currentFlowData.value.nodes.forEach((node) => {
    if (node.isNew) {
      newNodes.push({ ...node });
    } else if (node.isUpdated) {
      updatedNodes.push({ ...node });
    }
  });

  // 收集新增和修改的边
  // 注意：只收集当前存在的边，"新增后删除"的边不会出现在这里
  currentFlowData.value.edges.forEach((edge) => {
    if (edge.isNew) {
      newEdges.push({ ...edge });
    } else if (edge.isUpdated) {
      updatedEdges.push({ ...edge });
    }
  });

  // 发出保存事件，包含分类的变更数据
  emit('save', {
    newNodes,
    updatedNodes,
    newEdges,
    updatedEdges,
    deletedNodes: [...deletedNodes.value],
    deletedEdges: [...deletedEdges.value],
  });

  // 保存成功后，重置变更标记
  resetChangeMarks();
  message.success('保存成功');
};

// 重置变更标记
const resetChangeMarks = () => {
  currentFlowData.value.nodes.forEach((node) => {
    node.isNew = undefined;
    node.isUpdated = undefined;
    node.isDeleted = undefined;
  });

  currentFlowData.value.edges.forEach((edge) => {
    edge.isNew = undefined;
    edge.isUpdated = undefined;
    edge.isDeleted = undefined;
  });

  // 清空删除记录
  deletedNodes.value = [];
  deletedEdges.value = [];

  // 更新原始数据
  initializeOriginalData();
};

// 暴露的方法
defineExpose({
  getFlowData: () => currentFlowData.value,
  setFlowData: (data: WorkflowData) => {
    currentFlowData.value = data;
    if (lf.value) {
      lf.value.render(data);
    }
  },
  validate: () => validateWorkflow(currentFlowData.value),
  exportData: (format = 'json') => {
    if (format === 'json') {
      return JSON.stringify(currentFlowData.value, null, 2);
    }
    return '';
  },
  importData: (data: string, format = 'json') => {
    try {
      if (format === 'json') {
        const parsedData = JSON.parse(data);
        currentFlowData.value = parsedData;
        if (lf.value) {
          lf.value.render(parsedData);
        }
        return true;
      }
    } catch {
      message.error('数据格式错误');
      return false;
    }
    return false;
  },
  clear: clearCanvas,
  fitView,
  zoom: (ratio: number) => {
    if (lf.value) {
      lf.value.zoom(ratio > 1);
    }
  },
  undo,
  redo,
  getSelectedElements: () => {
    if (!lf.value) return { nodes: [], edges: [] };
    const selectedElements = lf.value.getSelectElements();
    return {
      nodes: selectedElements.nodes as WorkflowNode[],
      edges: selectedElements.edges as WorkflowEdge[],
    };
  },
  deleteSelectedElements: () => {
    if (lf.value) {
      const selectedElements = lf.value.getSelectElements();
      selectedElements.nodes.forEach((node) => lf.value?.deleteNode(node.id));
      selectedElements.edges.forEach((edge) => lf.value?.deleteEdge(edge.id));
      updateFlowData();
    }
  },
});
</script>

<template>
  <div class="workflow-designer">
    <!-- 工具栏 -->
    <div class="workflow-toolbar">
      <div class="toolbar-left">
        <Space>
          <ButtonGroup size="small">
            <Button @click="undo" :disabled="!canUndo">
              <template #icon><Undo class="h-4 w-4" /></template>
              撤销
            </Button>
            <Button @click="redo" :disabled="!canRedo">
              <template #icon><Redo class="h-4 w-4" /></template>
              重做
            </Button>
          </ButtonGroup>

          <ButtonGroup size="small">
            <Button @click="zoomIn">
              <template #icon><ZoomIn class="h-4 w-4" /></template>
            </Button>
            <Button @click="zoomOut">
              <template #icon><ZoomOut class="h-4 w-4" /></template>
            </Button>
            <Button @click="fitView">
              <template #icon><Expand class="h-4 w-4" /></template>
              适应画布
            </Button>
          </ButtonGroup>

          <Button size="small" @click="toggleMiniMap">
            <template #icon><Image class="h-4 w-4" /></template>
            缩略图
          </Button>
        </Space>
      </div>

      <div class="toolbar-right">
        <Space>
          <!-- <Button size="small" @click="validateFlow">
            <template #icon><CheckCircle class="h-4 w-4" /></template>
            验证
          </Button>
          <Button size="small" @click="exportData">
            <template #icon><Download class="h-4 w-4" /></template>
            导出
          </Button> -->
          <Button size="small" @click="showDataModal = true">
            <template #icon><Eye class="h-4 w-4" /></template>
            查看数据
          </Button>
          <Button size="small" @click="clearCanvas" danger>
            <template #icon><Trash2 class="h-4 w-4" /></template>
            清空
          </Button>
          <Button size="small" @click="handleSave" type="primary">
            <template #icon><Save class="h-4 w-4" /></template>
            保存
          </Button>
        </Space>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="workflow-content">
      <!-- 节点面板 -->
      <div class="workflow-sidebar" v-if="!readonly">
        <WorkflowNodePanel
          :node-configs="nodeConfigs"
          @drag-node="handleDragNode"
          @add-node="handleAddNode"
        />
      </div>

      <!-- 画布区域 -->
      <div class="workflow-canvas">
        <div ref="canvasContainer" class="canvas-container"></div>
      </div>
    </div>

    <!-- 属性面板 -->
    <Drawer
      v-model:open="propertyDrawerVisible"
      title="属性设置"
      placement="right"
      width="400"
      @close="closePropertyDrawer"
    >
      <WorkflowPropertyPanel
        v-if="selectedElement && selectedElementType"
        :element="selectedElement"
        :element-type="selectedElementType"
        :approver-data-source="approverDataSource"
        @update="handleElementUpdate"
        @confirm="handleElementConfirm"
      />
    </Drawer>

    <!-- 数据查看模态框 -->
    <Modal
      v-model:open="showDataModal"
      title="工作流数据"
      width="80%"
      :footer="null"
    >
      <Tabs>
        <Tabs.TabPane key="json" tab="JSON格式">
          <pre class="data-preview">{{
            JSON.stringify(currentFlowData, null, 2)
          }}</pre>
        </Tabs.TabPane>
        <Tabs.TabPane key="nodes" tab="节点列表">
          <Table
            :columns="nodeColumns"
            :data-source="currentFlowData.nodes"
            size="small"
            :pagination="false"
          />
        </Tabs.TabPane>
        <Tabs.TabPane key="edges" tab="连线列表">
          <Table
            :columns="edgeColumns"
            :data-source="currentFlowData.edges"
            size="small"
            :pagination="false"
          />
        </Tabs.TabPane>
      </Tabs>
    </Modal>

    <!-- 验证结果模态框 -->
    <Modal
      v-model:open="showValidationModal"
      title="验证结果"
      width="600"
      :footer="null"
    >
      <div class="validation-result">
        <Result
          :status="validationResult?.valid ? 'success' : 'warning'"
          :title="validationResult?.valid ? '验证通过' : '发现问题'"
          :sub-title="
            validationResult?.valid
              ? '工作流配置正确'
              : `发现 ${validationResult?.errors.length} 个问题`
          "
        />

        <div
          v-if="validationResult && validationResult.errors.length > 0"
          class="validation-errors"
        >
          <List :data-source="validationResult.errors" size="small">
            <template #renderItem="{ item }">
              <ListItem>
                <ListItemMeta>
                  <template #avatar>
                    <Tag :color="item.type === 'error' ? 'red' : 'orange'">
                      {{ item.type === 'error' ? '错误' : '警告' }}
                    </Tag>
                  </template>
                  <template #title>{{ item.message }}</template>
                  <template #description v-if="item.nodeId || item.edgeId">
                    {{
                      item.nodeId
                        ? `节点: ${item.nodeId}`
                        : `连线: ${item.edgeId}`
                    }}
                  </template>
                </ListItemMeta>
              </ListItem>
            </template>
          </List>
        </div>
      </div>
    </Modal>

    <!-- 新增节点模态框 -->
    <Modal
      v-model:open="showAddNodeModal"
      title="选择节点类型"
      :footer="null"
      width="400px"
    >
      <div class="grid grid-cols-2 gap-4">
        <Button
          v-for="nodeType in ['start', 'userTask', 'serviceTask', 'end']"
          :key="nodeType"
          type="primary"
          size="large"
          block
          @click="addNodeAndConnect(nodeType)"
        >
          {{
            nodeType === 'start'
              ? '开始节点'
              : nodeType === 'userTask'
                ? '用户任务'
                : nodeType === 'serviceTask'
                  ? '服务任务'
                  : '结束节点'
          }}
        </Button>
      </div>
    </Modal>

    <!-- 右键菜单 -->
    <div
      v-if="contextMenuVisible"
      class="context-menu"
      :style="{
        position: 'fixed',
        left: `${contextMenuPosition.x}px`,
        top: `${contextMenuPosition.y}px`,
        zIndex: 9999,
      }"
      @click.stop
    >
      <div class="context-menu-content">
        <div class="context-menu-item" @click="handleContextMenuAction('edit')">
          编辑
        </div>
        <div
          class="context-menu-item"
          @click="handleContextMenuAction('delete')"
        >
          删除
        </div>
        <div
          v-if="contextMenuType === 'node'"
          class="context-menu-item"
          @click="handleContextMenuAction('addNode')"
        >
          新增节点
        </div>
      </div>
    </div>

    <!-- 遮罩层，点击隐藏菜单 -->
    <div
      v-if="contextMenuVisible"
      class="context-menu-overlay"
      @click="hideContextMenu"
    ></div>
  </div>
</template>

<style scoped>
.workflow-designer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f5f5f5;
}

.workflow-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 1px 4px rgb(0 0 0 / 10%);
}

.workflow-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.workflow-sidebar {
  width: 240px;
  overflow-y: auto;
  background: white;
  border-right: 1px solid #e8e8e8;
}

.workflow-canvas {
  position: relative;
  flex: 1;
}

.canvas-container {
  width: 100%;
  height: 100%;
}

.data-preview {
  max-height: 400px;
  padding: 16px;
  overflow: auto;
  font-size: 12px;
  background: #f5f5f5;
  border-radius: 4px;
}

.validation-result {
  text-align: center;
}

.validation-errors {
  margin-top: 16px;
  text-align: left;
}

.context-menu-overlay {
  position: fixed;
  inset: 0;
  z-index: 9998;
  background: transparent;
}

.context-menu {
  overflow: hidden;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgb(0 0 0 / 15%);
}

.context-menu-content {
  min-width: 120px;
}

.context-menu-item {
  padding: 8px 16px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  transition: background-color 0.2s;
}

.context-menu-item:hover {
  background-color: #f5f5f5;
}

.context-menu-item:active {
  background-color: #e6f7ff;
}
</style>
