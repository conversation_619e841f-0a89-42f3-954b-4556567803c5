# 菱形条件节点使用指南

## 概述

菱形条件节点是工作流中用于实现分支逻辑的核心组件。它采用标准的菱形（Diamond）形状，符合流程图的国际标准，用于根据特定条件将流程分为不同的执行路径。

## 节点特性

### 外观特征

- **形状**: 标准菱形（Diamond）
- **颜色**: 橙色主题 (#fa8c16)
- **尺寸**: 80x60 像素
- **文字**: 白色粗体，居中显示

### 连接规则

- **输入连接**: 最多1个（从顶部锚点）
- **输出连接**: 最多2个（右侧为True，左侧为False）
- **最少输出**: 1个（至少需要一个输出分支）

### 锚点配置

```
     ↑ (输入)
     |
← False  True →
     |
     ↓
```

## 使用方法

### 1. 添加节点

- 从节点面板拖拽条件节点到画布
- 或者点击节点面板中的条件节点按钮

### 2. 配置属性

点击节点后，在右侧属性面板中配置：

- **节点名称**: 显示在节点上的名称
- **判断条件**: 具体的条件表达式
- **True分支标签**: True输出的标签文字
- **False分支标签**: False输出的标签文字
- **描述**: 节点的详细说明

### 3. 连接节点

- 从条件节点的右侧锚点连接True分支
- 从条件节点的左侧锚点连接False分支
- 向条件节点的顶部锚点连接输入

## 条件表达式示例

### 基础比较

```javascript
// 数值比较
age > 18;
amount >= 1000;
score < 60;

// 字符串比较
status === 'approved';
name !== '';
type === 'urgent';
```

### 逻辑运算

```javascript
// AND 逻辑
age >= 18 && age <= 65;
score >= 60 && attendance > 0.8;

// OR 逻辑
role === 'admin' || role === 'manager';
priority === 'high' || priority === 'urgent';

// NOT 逻辑
!isCompleted;
status !== 'cancelled';
```

### 复杂条件

```javascript
// 范围判断
price >= 100 && price <= 500 && category === 'electronics';

// 数组操作
roles.includes('admin');
tags.length > 0;
permissions.indexOf('write') >= 0;

// 对象属性
user.isActive && user.level > 2;
order.status === 'paid' && order.items.length > 0;
```

### 空值检查

```javascript
// 基础空值检查
data != null;
value !== undefined;
text !== '';

// 综合检查
data != null && data !== '' && data.length > 0;
user && user.id && user.isActive;
```

## 最佳实践

### 1. 条件表达式编写

- **简洁明了**: 避免过于复杂的条件表达式
- **可读性强**: 使用有意义的变量名和操作符
- **防御性编程**: 添加必要的空值检查

### 2. 节点命名

- **描述性**: 节点名称应该清楚地描述判断内容
- **简短**: 保持名称简洁，避免过长的文字
- **一致性**: 在同一工作流中保持命名风格一致

### 3. 分支标签

- **明确**: True/False分支的标签应该明确易懂
- **业务相关**: 使用业务术语，如"通过/拒绝"、"是/否"等
- **避免歧义**: 确保标签不会产生歧义

### 4. 工作流设计

- **避免过深嵌套**: 过多的条件嵌套会降低可读性
- **考虑异常情况**: 为异常情况设计适当的处理分支
- **文档化**: 为复杂的条件逻辑添加注释说明

## 常见模式

### 1. 审批流程

```
申请提交 → 条件(金额>10000?) → True: 需要高级审批
                              → False: 普通审批
```

### 2. 数据验证

```
数据输入 → 条件(数据完整?) → True: 继续处理
                        → False: 返回修改
```

### 3. 权限检查

```
用户操作 → 条件(有权限?) → True: 执行操作
                      → False: 拒绝访问
```

### 4. 业务规则

```
订单处理 → 条件(库存充足?) → True: 发货
                        → False: 缺货处理
```

## 注意事项

1. **条件表达式语法**: 确保使用正确的JavaScript语法
2. **数据类型**: 注意比较时的数据类型匹配
3. **空值处理**: 避免空值导致的条件判断错误
4. **性能考虑**: 复杂条件可能影响执行性能
5. **测试验证**: 对所有可能的条件分支进行测试

## 错误排查

### 常见错误

1. **语法错误**: 检查条件表达式的语法
2. **类型错误**: 确认比较的数据类型
3. **空引用**: 检查变量是否存在
4. **逻辑错误**: 验证条件逻辑是否正确

### 调试技巧

1. **分步调试**: 将复杂条件分解为简单条件
2. **日志输出**: 在条件判断前后添加日志
3. **单元测试**: 为条件逻辑编写单元测试
4. **数据验证**: 确认输入数据的正确性

通过遵循以上指南，你可以有效地使用菱形条件节点来构建复杂的工作流程逻辑。
