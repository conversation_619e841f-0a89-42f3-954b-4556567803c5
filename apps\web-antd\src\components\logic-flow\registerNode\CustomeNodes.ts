import type LogicFlow from '@logicflow/core';

// 注册开始节点
export function registerStartNode(lf: LogicFlow) {
  lf.register('start', ({ CircleNode, CircleNodeModel, h }: any) => {
    class StartNode extends CircleNode {
      getShape() {
        const { model } = (this as any).props;
        const { x, y, r } = model;
        const { fill, stroke, strokeWidth } = model.getNodeStyle();

        return h('g', {}, [
          h('circle', {
            cx: x,
            cy: y,
            r,
            fill,
            stroke,
            strokeWidth,
          }),
          h(
            'text',
            {
              x,
              y: y + 4,
              textAnchor: 'middle',
              fill: '#fff',
              fontSize: 12,
            },
            '开始',
          ),
        ]);
      }
    }

    class StartModel extends CircleNodeModel {
      getConnectedTargetRules() {
        const rules = super.getConnectedTargetRules();
        rules.push({
          message: '开始节点不能作为连线的终点',
          validate: () => false,
        });
        return rules;
      }

      getNodeStyle() {
        const style = super.getNodeStyle();
        style.fill = '#52c41a';
        style.stroke = '#389e0d';
        return style;
      }

      initNodeData(data: any) {
        data.text = {
          value: data.properties?.name || '开始',
          x: data.x,
          y: data.y + 35,
          draggable: false,
          editable: true,
        };
        super.initNodeData(data);
        (this as any).r = 20;
      }
    }

    return { view: StartNode, model: StartModel };
  });
}

// 注册结束节点
export function registerEndNode(lf: LogicFlow) {
  lf.register('end', ({ CircleNode, CircleNodeModel, h }: any) => {
    class EndNode extends CircleNode {
      getShape() {
        const { model } = (this as any).props;
        const { x, y, r } = model;
        const { fill, stroke, strokeWidth } = model.getNodeStyle();

        return h('g', {}, [
          h('circle', {
            cx: x,
            cy: y,
            r,
            fill,
            stroke,
            strokeWidth,
          }),
          h(
            'text',
            {
              x,
              y: y + 4,
              textAnchor: 'middle',
              fill: '#fff',
              fontSize: 12,
            },
            '结束',
          ),
        ]);
      }
    }

    class EndModel extends CircleNodeModel {
      getConnectedSourceRules() {
        const rules = super.getConnectedSourceRules();
        rules.push({
          message: '结束节点不能作为连线的起点',
          validate: () => false,
        });
        return rules;
      }

      getNodeStyle() {
        const style = super.getNodeStyle();
        style.fill = '#f5222d';
        style.stroke = '#cf1322';
        return style;
      }

      initNodeData(data: any) {
        data.text = {
          value: data.properties?.name || '结束',
          x: data.x,
          y: data.y + 35,
          draggable: false,
          editable: true,
        };
        super.initNodeData(data);
        (this as any).r = 20;
      }
    }

    return { view: EndNode, model: EndModel };
  });
}

// 注册审批节点
export function registerApprovalNode(lf: LogicFlow) {
  lf.register('approval', ({ RectNode, RectNodeModel, h }: any) => {
    class ApprovalNode extends RectNode {
      getShape() {
        const { model } = (this as any).props;
        const { x, y, width, height } = model;
        const { fill, stroke, strokeWidth } = model.getNodeStyle();

        // 使用transform来正确定位节点图形
        const transform = `matrix(1 0 0 1 ${x - width / 2} ${y - height / 2})`;

        return h('g', { transform }, [
          h('rect', {
            x: 0,
            y: 0,
            width,
            height,
            fill,
            stroke,
            strokeWidth,
            rx: 4,
            ry: 4,
          }),
          h(
            'text',
            {
              x: width / 2,
              y: height / 2 + 4,
              textAnchor: 'middle',
              fill: '#fff',
              fontSize: 12,
            },
            '审批',
          ),
        ]);
      }
    }

    class ApprovalModel extends RectNodeModel {
      getNodeStyle() {
        const style = super.getNodeStyle();
        style.fill = '#1890ff';
        style.stroke = '#096dd9';
        return style;
      }

      initNodeData(data: any) {
        data.text = {
          value: data.properties?.name || '审批',
          x: data.x,
          y: data.y + 35,
          draggable: false,
          editable: true,
        };
        super.initNodeData(data);
        (this as any).width = 80;
        (this as any).height = 40;
      }
    }

    return { view: ApprovalNode, model: ApprovalModel };
  });
}

// 注册条件节点（菱形）
export function registerConditionNode(lf: LogicFlow) {
  lf.register('condition', ({ PolygonNode, PolygonNodeModel, h }: any) => {
    class ConditionNode extends PolygonNode {
      getShape() {
        const { model } = this.props;
        const { x, y, points } = model;
        const pointStr = points
          .map((point) => {
            return `${point[0] + x - model.width / 2}, ${point[1] + y - model.height / 2}`;
          })
          .join(' ');
        // const [top, right, bottom, left] = points;
        // const pointStr = [
        //   `${top[0] + x - model.width / 2}, ${top[1] + y - model.height / 2}`,
        //   `${right[0] + x - model.width / 2}, ${right[1] + y - model.height / 2}`,
        //   `${bottom[0] + x - model.width / 2}, ${bottom[1] + y - model.height / 2}`,
        //   `${left[0] + x - model.width / 2}, ${left[1] + y - model.height / 2}`,
        // ].join(' ');

        const style = model.getNodeStyle();

        return h('g', {}, [
          // 绘制菱形
          h('polygon', {
            ...style,
            points: pointStr,
          }),
          // 绘制文本
          h(
            'text',
            {
              x,
              y: y + 4,
              textAnchor: 'middle',
              fill: '#fff',
              fontSize: 12,
              fontWeight: 'bold',
            },
            model.properties?.name || '条件',
          ),
        ]);
      }
    }

    class ConditionModel extends PolygonNodeModel {
      getConnectedSourceRules() {
        const rules = super.getConnectedSourceRules();
        // 条件节点应该有两个输出：是(True)和否(False)
        rules.push({
          message: '条件节点最多只能有两个输出连线',
          validate: (sourceNode: any) => {
            const edges = this.graphModel.edges.filter(
              (edge: any) => edge.sourceNodeId === sourceNode.id,
            );
            return edges.length < 2;
          },
        });
        return rules;
      }

      // 可以添加自定义的锚点来控制连线的连接位置
      getDefaultAnchor() {
        const { x, y } = this;
        return [
          { x, y: y - 30, id: 'top' }, // 顶部锚点（输入）
          { x: x + 40, y, id: 'right' }, // 右侧锚点（True输出）
          { x, y: y + 30, id: 'bottom' }, // 底部锚点
          { x: x - 40, y, id: 'left' }, // 左侧锚点（False输出）
        ];
      }

      getNodeStyle() {
        const style = super.getNodeStyle();
        style.fill = '#fa8c16';
        style.stroke = '#d46b08';
        style.strokeWidth = 2;
        return style;
      }

      initNodeData(data: any) {
        data.text = {
          value: data.properties?.name || '条件',
          x: data.x,
          y: data.y + 45,
          draggable: false,
          editable: true,
        };
        super.initNodeData(data);

        // 设置标准菱形的四个顶点（相对于节点中心的偏移量）
        (this as any).points = [
          [0, -30],
          [40, 0],
          [0, 30],
          [-40, 0], // 左顶点
        ];
        // (this as any).points = [
        //   [0, -30 - (this as any)._height / 2], // 上顶点
        //   [40, 0 - (this as any)._width / 2], // 右顶点
        //   [0, 30 + (this as any)._height / 2], // 下顶点
        //   [-40, 0 - (this as any)._width / 2], // 左顶点
        // ];
      }
    }

    return { view: ConditionNode, model: ConditionModel };
  });
}

/**
 * 注册所有流程节点
 */
export function registerAllCustomNodes(lf: LogicFlow) {
  registerStartNode(lf);
  registerEndNode(lf);
  registerApprovalNode(lf);
  registerConditionNode(lf);
}
