<script setup lang="ts">
import { useVbenModal } from '@vben/common-ui';
import type { FormApi } from '#/api/equipment/persons-manage';
import { methodColumns, fileColumns } from '../persons-manage-data';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  getListCertificationsApi,
  getTestDocApi,
  deleteRowsApi,
  getTestUsrnamApi,
  deleteDocumentationApi,
} from '#/api/equipment/persons-manage';
import { ref, watch } from 'vue';
import type {
  VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import {
  Button,
  Space,
  message,
  Upload,
} from 'ant-design-vue';
import AuthorizeModal from './authorize-modal.vue';
import OneClickAuthorizeModal from './oneclick-authorize-modal.vue';
import { uploadWitlabFile, downloadWitlabFile } from '#/api/core/witlab';
import type { UploadChangeParam } from 'ant-design-vue';

interface RowType {
  [key: string]: any;
}
const props = defineProps<{
  clickRow: RowType;
}>();
const clickMethodRow = ref<RowType>({});
const fileClickRow = ref<RowType>({});

const [AuthorizeFormModal, authorizeFormModalApi] = useVbenModal({
  connectedComponent: AuthorizeModal,
  destroyOnClose: true,
});
const [OneClickFormModal, oneClickAuthorizeModalApi] = useVbenModal({
  connectedComponent: OneClickAuthorizeModal,
  destroyOnClose: true,
});

watch(
  () => props.clickRow,
  (row) => {
    if (row) {
      gridApi.query();
    }
  },
);
watch(clickMethodRow, (row) => {
  if (row) {
    fileGridApi.query();
  }
});
const fileGridEvents: VxeGridListeners<FormApi.Form> = {
  currentChange: async ({ row }) => {
    if (row) {
      fileClickRow.value = row;
    }
  },
};
const gridOptions: VxeTableGridOptions<FormApi.Form> = {
  columns: methodColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        const data = props.clickRow
          ? await getListCertificationsApi([props.clickRow.USRNAM])
          : [];
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  keepSource: true,
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};
const gridEvents: VxeGridListeners<FormApi.Form> = {
  currentChange: async ({ row }) => {
    if (row) {
      clickMethodRow.value = row;
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents: gridEvents,
});
const fileGridOptions: VxeTableGridOptions<FormApi.Form> = {
  columns: fileColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        if (!props.clickRow || !clickMethodRow.value) {
          return [];
        }
        const params = [props.clickRow.USRNAM, clickMethodRow.value.ORIGREC];
        const data = await getTestDocApi(params);
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  keepSource: true,
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};

const [FileGrid, fileGridApi] = useVbenVxeGrid({
  gridOptions: fileGridOptions,
  gridEvents: fileGridEvents,
});
const onRefresh = () => {
  gridApi.query();
  fileGridApi.query();
};
const addAuthorization = () => {
  authorizeFormModalApi.setData({ clickRow: props.clickRow }).open();
};
const cancelAuthorization = async () => {
  const oESig = false;
  if (oESig) {
    message.warning('请先进行电子签名');
    return;
  }  if (!clickMethodRow.value) {
    return;
  }
  const params = ['METHODCERT', [clickMethodRow.value.ORIGREC]];
  await deleteRowsApi(params);
  gridApi.query();
  fileGridApi.query();
};
const batchAuthorization = async () => {
  if (!clickMethodRow.value.ORIGREC) {
    message.warning('请选择需要授权的数据');
    return;
  }
  const res = await getTestUsrnamApi([
    [clickMethodRow.value.ORIGREC],
    [props.clickRow.USRNAM],
  ]);
  let usrnam = [];
  if (res && res.length > 0) {
    usrnam = res;
  }
  oneClickAuthorizeModalApi
    .setData({ clickRow: clickMethodRow.value, usrnam: usrnam, type: 'method' })
    .open();
};
const removeAttachment = async () => {
  if (!fileClickRow.value.ORIGREC) {
    message.warning('请选择文件');
    return;
  }
  //TODO:电子签名
    const oESig = false;
  if (oESig) {
    message.warning('请先进行电子签名');
    return;
  }
  const params = [fileClickRow.value.ORIGREC, oESig];
  await deleteDocumentationApi(params);
  fileGridApi;
};
const viewAttachment = async () => {
  if (!fileClickRow.value.ORIGREC) {
    message.warning('请选择文件');
    return;
  }
  await downloadWitlabFile(
    fileClickRow.value.STARDOC_ID,
    fileClickRow.value.FILENAME,
  );
};
const fileList = ref([]);
const headers = {
  authorization: 'authorization-text',
};
const appendAttachment = async (info: UploadChangeParam) => {
  if (info.file && info.file.originFileObj) {
    await uploadWitlabFile(info.file.originFileObj);
  }
};
</script>
<template>
  <AuthorizeFormModal @success="onRefresh" />
  <OneClickFormModal @success="onRefresh" />
  <Grid class="h-[47%]">
    <template #toolbar-actions>
      <Space :size="[8, 0]" wrap>
        <Button type="primary" @click="addAuthorization">
          {{ $t('equipment.persons-manage.addAuthorization') }}
        </Button>
        <Button type="primary" @click="cancelAuthorization">
          {{ $t('equipment.persons-manage.cancelAuthorization') }}
        </Button>
        <Button type="primary" @click="batchAuthorization">
          {{ $t('equipment.persons-manage.batchAuthorization') }}
        </Button>
        <Upload
          v-model:file-list="fileList"
          name="file"
          :showUploadList="false"
          :headers="headers"
          :max-count="1"
          @change="appendAttachment"
        >
          <Button type="primary">
            {{ $t('equipment.persons-manage.appendAttachment') }}
          </Button>
        </Upload>
        <Button type="primary" @click="removeAttachment">
          {{ $t('equipment.persons-manage.removeAttachment') }}
        </Button>
        <Button type="primary" @click="viewAttachment">
          {{ $t('equipment.persons-manage.viewAttachment') }}
        </Button>
      </Space>
    </template>
  </Grid>
  <FileGrid class="h-[47.5%]"> </FileGrid>
</template>
