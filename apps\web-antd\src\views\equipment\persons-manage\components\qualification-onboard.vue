<script setup lang="ts">
import type { FormApi } from '#/api/equipment/persons-manage';
import { boardColumns, fileColumns } from '../persons-manage-data';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  getCredentialsApi,
  getCredentialsDocApi,
  updateProviderApi,
  deleteDocumentation<PERSON>pi,
  deleteCredentialsApi,
} from '#/api/equipment/persons-manage';
import { ref, watch } from 'vue';
import type {
  VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import {
  Button,
  Space,
  message,
  Upload,
  Modal as AModal,
} from 'ant-design-vue';
import CertificateModal from './certificate-modal.vue';
import { useVbenModal } from '@vben/common-ui';
import { uploadWitlabFile, downloadWitlabFile } from '#/api/core/witlab';
import type { UploadChangeParam } from 'ant-design-vue';

const [CertificateFormModal, certificateModalApi] = useVbenModal({
  connectedComponent: CertificateModal,
  destroyOnClose: true,
});
interface RowType {
  [key: string]: any;
}
const props = defineProps<{
  clickRow: RowType | null;
}>();
const clickMethodRow = ref<RowType>({});
const fileClickRow = ref<RowType>({});

watch(
  () => props.clickRow,
  (row) => {
    if (row) {
      gridApi.query();
    }
  },
);
watch(clickMethodRow, (row) => {
  if (row) {
    fileGridApi.query();
  }
});
const gridOptions: VxeTableGridOptions<FormApi.Form> = {
  columns: boardColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        const data = props.clickRow
          ? await getCredentialsApi([props.clickRow.USRNAM])
          : [];
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  keepSource: true,
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};
const gridEvents: VxeGridListeners<FormApi.Form> = {
  currentChange: async ({ row }) => {
    if (row) {
      clickMethodRow.value = row;
    }
  },
};
const fileGridEvents: VxeGridListeners<FormApi.Form> = {
  currentChange: async ({ row }) => {
    if (row) {
      fileClickRow.value = row;
    }
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents: gridEvents,
});
const fileGridOptions: VxeTableGridOptions<FormApi.Form> = {
  columns: fileColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        if (!props.clickRow || !clickMethodRow.value) {
          return [];
        }
        const params = [props.clickRow.USRNAM, clickMethodRow.value.ORIGREC];
        const data = await getCredentialsDocApi(params);
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  keepSource: true,
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};

const [FileGrid, fileGridApi] = useVbenVxeGrid({
  gridOptions: fileGridOptions,
  gridEvents: fileGridEvents,
});
const onRefresh = () => {
  gridApi.query();
  fileGridApi.query();
};
const addCertificate = () => {
  certificateModalApi.setData({ clickRow: props.clickRow }).open();
};
const removeCertificate = () => {
  if (!clickMethodRow.value) {
    message.warning('请选择一行');
    return;
  }
  //TODO:电子签名
  const oESig = false;
  if (oESig) {
    message.warning('请先进行电子签名');
    return;
  }
  AModal.confirm({
    title: '提示',
    content: '确定要移除选定的证书吗？',
    onOk: async () => {
      const res = await deleteCredentialsApi([clickMethodRow?.value.ORIGREC]);
      if (res) {
        gridApi.query();
      }
    },
  });
};
const removeAttachment = async () => {
  if (!fileClickRow.value.ORIGREC) {
    message.warning('请选择文件');
    return;
  }
  //TODO:电子签名
  const oESig = '电子签名';
  const params = [fileClickRow.value.ORIGREC, oESig];
  await deleteDocumentationApi(params);
  fileGridApi;
};
const viewAttachment = async () => {
  if (!fileClickRow.value.ORIGREC) {
    message.warning('请选择文件');
    return;
  }
  await downloadWitlabFile(
    fileClickRow.value.STARDOC_ID,
    fileClickRow.value.FILENAME,
  );
};
const headers = {
  authorization: 'authorization-text',
};
const customUpload = async (e) => {
  const res = await uploadWitlabFile(e.file);
  console.warn(res)
};
const hasEditStatus = (row: RowType) => {
  return gridApi.grid?.isEditByRow(row);
};

const editRowEvent = (row: RowType) => {
  gridApi.grid?.setEditRow(row);
};

const saveRowEvent = async () => {
  await gridApi.grid?.clearEdit();
  if (!clickMethodRow.value) {
    message.warning('未选择行，无法保存');
    return;
  }
  const row = clickMethodRow.value;
  const rowList = Object.keys(row).map((key) => {
    const isNum = typeof row[key] === 'number' ? 'N' : 'S';
    return [key, row[key], isNum, ''];
  });
  const params = ['dgCredentials', 'CREDENTIALS', rowList, row.ORIGREC, null];
  await updateProviderApi(params);
};

const cancelRowEvent = (row: RowType) => {
  gridApi.grid?.clearEdit().then(() => {
    // 还原行数据
    gridApi.grid.revertData(row);
  });
};
</script>
<template>
  <CertificateFormModal @success="onRefresh" />
  <Grid class="h-[47%]">
    <template #toolbar-actions>
      <Space :size="[8, 0]" wrap>
        <Button type="primary" @click="addCertificate">
          {{ $t('equipment.persons-manage.addCertificate') }}
        </Button>
        <Button type="primary" @click="removeCertificate">
          {{ $t('equipment.persons-manage.removeCertificate') }}
        </Button>
        <Upload
          name="file"
          :showUploadList="false"
          :headers="headers"
          :max-count="1"
          :customRequest="customUpload"
        >
          <Button type="primary">
            {{ $t('equipment.persons-manage.appendAttachment') }}
          </Button>
        </Upload>

        <Button type="primary" @click="removeAttachment">
          {{ $t('equipment.persons-manage.removeAttachment') }}
        </Button>
        <Button type="primary" @click="viewAttachment">
          {{ $t('equipment.persons-manage.viewAttachment') }}
        </Button>
      </Space>
    </template>
    <template #action="{ row }">
      <template v-if="hasEditStatus(row)">
        <Button type="link" @click="saveRowEvent()">
          {{ $t('business-static-tables.save') }}
        </Button>
        <Button type="link" @click="cancelRowEvent(row)">
          {{ $t('business-static-tables.cancel') }}
        </Button>
      </template>
      <template v-else>
        <Button type="link" @click="editRowEvent(row)">
          {{ $t('business-static-tables.edit') }}
        </Button>
      </template>
    </template>
  </Grid>
  <FileGrid class="h-[47.5%]"> </FileGrid>
</template>
