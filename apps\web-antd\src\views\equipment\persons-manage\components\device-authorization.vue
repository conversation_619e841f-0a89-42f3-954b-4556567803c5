<script setup lang="ts">
import type { FormApi } from '#/api/equipment/persons-manage';
import { deviceColumns, fileColumns } from '../persons-manage-data';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  getEqcertApi,
  getEquipDocApi,
  deleteDocumentation<PERSON>pi,
  deleteRowsapi,
} from '#/api/equipment/persons-manage';
import { ref, watch } from 'vue';
import type {
  VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import { Button, Space, message, Upload } from 'ant-design-vue';
import DevicesModal from './devices-modal.vue';
import { useVbenModal } from '@vben/common-ui';
import OneClickAuthorizeModal from './oneclick-authorize-modal.vue';
import { uploadWitlabFile, downloadWitlabFile } from '#/api/core/witlab';
import type { UploadChangeParam } from 'ant-design-vue';

const [DevicesListModal, devicesFormModalApi] = useVbenModal({
  connectedComponent: DevicesModal,
  destroyOnClose: true,
});
const [OneClickFormModal, oneClickAuthorizeModalApi] = useVbenModal({
  connectedComponent: OneClickAuthorizeModal,
  destroyOnClose: true,
});
interface RowType {
  [key: string]: any;
}
const props = defineProps<{
  clickRow: RowType | null;
}>();
const clickMethodRow = ref<RowType>({});

watch(
  () => props.clickRow,
  (row) => {
    if (row) {
      gridApi.query();
    }
  },
);
watch(clickMethodRow, (row) => {
  if (row) {
    fileGridApi.query();
  }
});
const gridOptions: VxeTableGridOptions<FormApi.Form> = {
  columns: deviceColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        const data = props.clickRow
          ? await getEqcertApi([props.clickRow.USRNAM])
          : [];
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  keepSource: true,
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};
const gridEvents: VxeGridListeners<FormApi.Form> = {
  currentChange: async ({ row }) => {
    if (row) {
      clickMethodRow.value = row;
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents: gridEvents,
});
const fileGridOptions: VxeTableGridOptions<FormApi.Form> = {
  columns: fileColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        if (!props.clickRow || !clickMethodRow.value) {
          return [];
        }
        const params = [props.clickRow.USRNAM, clickMethodRow.value.ORIGREC];
        const data = await getEquipDocApi(params);
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  keepSource: true,
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};
const fileClickRow = ref<RowType>({});

const fileGridEvents: VxeGridListeners<FormApi.Form> = {
  currentChange: async ({ row }) => {
    if (row) {
      fileClickRow.value = row;
    }
  },
};
const [FileGrid, fileGridApi] = useVbenVxeGrid({
  gridOptions: fileGridOptions,
  gridEvents: fileGridEvents,
});
const onRefresh = () => {
  gridApi.query();
  fileGridApi.query();
};
const addEquipment = () => {
  devicesFormModalApi
    .setData({ clickRow: props.clickRow, deviceRow: clickMethodRow.value })
    .open();
};
const oneClickAuth = () => {
  oneClickAuthorizeModalApi
    .setData({ clickRow: clickMethodRow.value, type: 'device' })
    .open();
};
const disableEquipment = async () => {
  const oESig = false;
  if (oESig) {
    message.warning('请先进行电子签名');
    return;
  }
  //TODO:电子签名
  const params = ['EQUIPMENTCERT', clickMethodRow.value.ORIGREC, oESig];
  const res = await deleteRowsapi(params);
  if (res) {
    gridApi.query();
  }
};
const removeAttachment = async () => {
  if (!fileClickRow.value.ORIGREC) {
    message.warning('请选择文件');
    return;
  }
  const oESig = false;
  if (oESig) {
    message.warning('请先进行电子签名');
    return;
  }
  //TODO:电子签名
  const params = [fileClickRow.value.ORIGREC, oESig];
  await deleteDocumentationApi(params);
  fileGridApi;
};
const viewAttachment = async () => {
  if (!fileClickRow.value.ORIGREC) {
    message.warning('请选择文件');
    return;
  }
  await downloadWitlabFile(
    fileClickRow.value.STARDOC_ID,
    fileClickRow.value.FILENAME,
  );
};
const fileList = ref([]);
const headers = {
  authorization: 'authorization-text',
};
const appendAttachment = async (info: UploadChangeParam) => {
  if (info.file && info.file.originFileObj) {
    await uploadWitlabFile(info.file.originFileObj);
  }
};
</script>
<template>
  <DevicesListModal @success="onRefresh" />
  <OneClickFormModal @success="onRefresh" />

  <Grid class="h-[47%]">
    <template #toolbar-actions>
      <Space :size="[8, 0]" wrap>
        <Button type="primary" @click="addEquipment">
          {{ $t('equipment.persons-manage.addEquipment') }}
        </Button>
        <Button type="primary" @click="disableEquipment">
          {{ $t('equipment.persons-manage.disableEquipment') }}
        </Button>
        <Button type="primary" @click="oneClickAuth">
          {{ $t('equipment.persons-manage.oneClickAuth') }}
        </Button>
        <Upload
          v-model:file-list="fileList"
          name="file"
          :showUploadList="false"
          :headers="headers"
          :max-count="1"
          @change="appendAttachment"
        >
          <Button type="primary">
            {{ $t('equipment.persons-manage.appendAttachment') }}
          </Button>
        </Upload>
        <Button type="primary" @click="removeAttachment">
          {{ $t('equipment.persons-manage.deleteAttachment') }}
        </Button>
        <Button type="primary" @click="viewAttachment">
          {{ $t('equipment.persons-manage.viewAttachment') }}
        </Button>
      </Space>
    </template>
  </Grid>
  <FileGrid class="h-[47.5%]"> </FileGrid>
</template>
