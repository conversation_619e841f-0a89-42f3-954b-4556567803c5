{"workflow": {"applicationRef": "引用窗体", "dataViewer": {"columns": {"id": "ID", "name": "名称", "sourceNode": "源节点", "targetNode": "目标节点", "type": "类型", "x": "X坐标", "y": "Y坐标"}, "edges": "连线列表", "json": "JSON格式", "nodes": "节点列表", "title": "工作流数据"}, "description": "工作流管理", "dispStatusField": "状态显示字段", "messages": {"connectionNotAllowed": "连接不被允许", "exportSuccess": "导出成功", "modeSwitch": "已切换到{mode}模式", "resetSuccess": "已重置", "saveSuccess": "保存成功", "templateLoaded": "已加载模板：{name}", "validationFailed": "发现 {errorCount} 个错误，{warningCount} 个警告", "validationPassed": "工作流验证通过"}, "mode": {"edit": "编辑模式", "switchToEdit": "切换到编辑模式", "switchToView": "切换到查看模式", "view": "查看模式"}, "nodePanel": {"categories": {"basic": "基础节点", "control": "控制节点", "process": "流程节点"}, "title": "节点面板"}, "nodes": {"approval": {"description": "审批节点，需要指定审批人", "name": "审批"}, "condition": {"description": "条件判断节点，根据条件分支", "name": "条件"}, "end": {"description": "工作流结束节点", "name": "结束"}, "merge": {"description": "合并节点，等待所有分支完成", "name": "合并"}, "parallel": {"description": "并行处理节点，同时执行多个分支", "name": "并行"}, "start": {"description": "工作流开始节点", "name": "开始"}}, "operation": "操作", "operators": {"contains": "包含文本", "ends_with": "结尾是", "eq": "等于", "gt": "大于", "gte": "大于等于", "in": "包含", "lt": "小于", "lte": "小于等于", "ne": "不等于", "not_in": "不包含", "starts_with": "开头是"}, "properties": {"actions": {"reset": "重置", "save": "保存"}, "approval": {"approvalType": "审批类型", "approvalTypes": {"all": "全部审批", "majority": "多数审批", "single": "单人审批"}, "approvers": "审批人", "approversPlaceholder": "请选择审批人", "autoApprove": "超时自动审批", "timeLimit": "审批时限（小时）", "title": "审批设置"}, "basic": {"description": "节点描述", "descriptionPlaceholder": "请输入节点描述", "name": "节点名称", "namePlaceholder": "请输入节点名称"}, "condition": {"addCondition": "添加条件", "field": "字段名", "fieldPlaceholder": "字段名", "operator": "操作符", "operatorPlaceholder": "操作符", "rules": "条件规则", "title": "条件设置", "value": "值", "valuePlaceholder": "值"}, "edge": {"condition": "条件表达式", "conditionPlaceholder": "请输入条件表达式", "label": "连线标签", "labelPlaceholder": "请输入连线标签"}, "parallel": {"addBranch": "添加分支", "branchPlaceholder": "分支", "branches": "分支设置", "title": "并行设置"}, "title": "属性设置"}, "rejectField": "退回标识字段", "status": {"edgeCount": "连线数量", "mode": "模式", "nodeCount": "节点数量", "selectedEdge": "选中连线", "selectedNode": "选中节点", "title": "当前状态"}, "statusField": "状态字段", "stepCode": "步骤代码", "tableRelated": "数据表", "templates": {"applied": "已应用模板", "conditional": "条件审批流程", "parallel": "并行审批流程", "simple": "简单审批流程", "title": "工作流模板"}, "title": "工作流管理", "toolbar": {"clear": "清空", "export": "导出", "fitView": "适应画布", "loadTemplate": "加载模板", "miniMap": "缩略图", "redo": "重做", "switchMode": "切换模式", "undo": "撤销", "validate": "验证", "viewCode": "查看代码", "viewData": "查看数据", "zoomIn": "放大", "zoomOut": "缩小"}, "validation": {"error": "错误", "failed": "发现问题", "failedDesc": "发现 {count} 个问题", "passed": "验证通过", "passedDesc": "工作流配置正确", "rules": {"circularDependency": "工作流中存在循环依赖", "endNodeAsSource": "结束节点不能作为连线的源节点", "incompleteApprover": "审批人信息不完整", "incompleteCondition": "条件配置不完整", "insufficientBranches": "并行节点至少需要2个分支", "invalidConnection": "连线配置错误", "invalidTimeLimit": "审批时限必须大于0", "isolatedNode": "发现孤立节点", "multipleStartNodes": "工作流只能包含一个开始节点", "noApprovers": "审批节点必须设置审批人", "noConditions": "条件节点必须设置判断条件", "noEndNode": "建议添加结束节点", "noIncomingEdge": "节点没有输入连线", "noNodeName": "建议为节点设置名称", "noNodes": "工作流至少需要包含一个节点", "noOutgoingEdge": "节点没有输出连线", "noStartNode": "工作流必须包含一个开始节点", "selfConnection": "节点不能连接到自身", "startNodeAsTarget": "开始节点不能作为连线的终点", "unreachableNode": "节点从开始节点不可达"}, "title": "验证结果", "warning": "警告"}, "workflow": "工作流", "workflowCode": "工作流代码", "workflowDesc": "描述", "workflowName": "工作流名称"}}