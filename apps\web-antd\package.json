{"name": "@vben/web-antd", "version": "5.5.4", "homepage": "https://vben.pro", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "apps/web-antd"}, "license": "MIT", "author": {"name": "vben", "email": "<EMAIL>", "url": "https://github.com/anncwb"}, "type": "module", "scripts": {"build": "pnpm vite build --mode production", "build:analyze": "pnpm vite build --mode analyze", "dev": "pnpm vite --mode development", "preview": "vite preview", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "imports": {"#/*": "./src/*"}, "dependencies": {"@flowgram.ai/fixed-layout-editor": "^0.2.15", "@flowgram.ai/free-layout-editor": "^0.2.15", "@logicflow/core": "^2.0.16", "@logicflow/extension": "^2.0.21", "@logicflow/vue-node-registry": "^1.0.18", "@vben-core/menu-ui": "workspace:*", "@vben-core/shadcn-ui": "workspace:*", "@vben/access": "workspace:*", "@vben/common-ui": "workspace:*", "@vben/constants": "workspace:*", "@vben/hooks": "workspace:*", "@vben/icons": "workspace:*", "@vben/layouts": "workspace:*", "@vben/locales": "workspace:*", "@vben/plugins": "workspace:*", "@vben/preferences": "workspace:*", "@vben/request": "workspace:*", "@vben/stores": "workspace:*", "@vben/styles": "workspace:*", "@vben/types": "workspace:*", "@vben/utils": "workspace:*", "@vueuse/core": "catalog:", "ant-design-vue": "catalog:", "dayjs": "catalog:", "lodash.groupby": "catalog:", "lucide-vue-next": "catalog:", "mitt": "^3.0.1", "pinia": "catalog:", "uuid": "^11.1.0", "vue": "catalog:", "vue-json-pretty": "^2.5.0", "vue-router": "catalog:", "vxe-pc-ui": "catalog:", "vxe-table": "catalog:"}, "devDependencies": {"@types/lodash.groupby": "catalog:", "@types/uuid": "^10.0.0"}}