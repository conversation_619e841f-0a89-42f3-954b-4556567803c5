import { computed, onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import { storeToRefs } from 'pinia';

import {
  getWorkflowByMenu,
  getWorkflowPermissionsByMenu,
} from '#/api/settings/workflow';
import { useWorkflowPermissionStore } from '#/store';

/**
 * 工作流权限管理Hook
 * 提供基于菜单的权限检查功能，支持缓存和响应式更新
 */
export function useWorkflowPermission() {
  const route = useRoute();
  const store = useWorkflowPermissionStore();
  const {
    menuPermissions,
    loading: storeLoading,
    config,
    workflowCode,
    stepCode,
  } = storeToRefs(store);

  // 当前菜单名称（响应式）
  const currentMenuName = computed(() => route.name as string);

  // 权限数据（响应式）
  const permissions = computed(
    () => menuPermissions.value[currentMenuName.value] || [],
  );

  // 加载状态（响应式）
  const loading = computed(
    () => storeLoading.value[currentMenuName.value] || false,
  );

  // 错误状态
  const error = ref<Error | null>(null);

  // 在组件挂载时处理页面刷新逻辑
  onMounted(() => {
    if (config.value.refreshOnPageLoad) {
      // 清理所有缓存时间戳，强制重新获取权限
      store.cacheTimestamp = {};
    }
  });

  /**
   * 加载菜单权限
   * @param menuName 菜单名称
   * @param forceRefresh 是否强制刷新缓存
   */
  async function loadMenuPermissions(menuName: string, forceRefresh = false) {
    if (!forceRefresh && store.isCacheValid(menuName)) {
      return store.getMenuPermissions(menuName);
    }

    if (store.getLoading(menuName)) {
      return store.getMenuPermissions(menuName);
    }

    try {
      error.value = null;
      store.setLoading(menuName, true);

      const permissionData = await getWorkflowPermissionsByMenu(menuName);
      const [_workflowCode, _stepCode] = await getWorkflowByMenu(menuName);

      store.setWorkflowCode(_workflowCode);
      store.setStepCode(_stepCode);
      store.setMenuPermissions(menuName, permissionData);

      return permissionData;
    } catch (error_) {
      error.value =
        error_ instanceof Error ? error_ : new Error('获取权限失败');
      throw error.value;
    } finally {
      store.setLoading(menuName, false);
    }
  }

  /**
   * 检查是否有指定权限
   * @param permission 权限码
   * @param menuName 菜单名称，默认使用当前路由
   */
  function workflowGranted(permission: string, menuName?: string): boolean {
    const targetMenu = menuName || currentMenuName.value;
    if (!targetMenu) return false;

    return store.hasPermission(targetMenu, permission);
  }

  /**
   * 检查是否有任意一个权限
   * @param permissions 权限码数组
   * @param menuName 菜单名称，默认使用当前路由
   */
  function workflowGrantedAny(
    permissions: string[],
    menuName?: string,
  ): boolean {
    return permissions.some((permission) =>
      workflowGranted(permission, menuName),
    );
  }

  /**
   * 检查是否有所有权限
   * @param permissions 权限码数组
   * @param menuName 菜单名称，默认使用当前路由
   */
  function workflowGrantedAll(
    permissions: string[],
    menuName?: string,
  ): boolean {
    return permissions.every((permission) =>
      workflowGranted(permission, menuName),
    );
  }

  /**
   * 刷新当前菜单权限
   */
  async function refreshPermissions() {
    if (currentMenuName.value) {
      await loadMenuPermissions(currentMenuName.value, true);
    }
  }

  /**
   * 清除权限缓存
   * @param menuName 菜单名称，不传则清除所有
   */
  function clearPermissionCache(menuName?: string) {
    store.clearMenuPermissions(menuName);
  }

  // 监听路由变化，自动加载权限
  watch(
    currentMenuName,
    async (newMenuName) => {
      if (newMenuName) {
        try {
          // 始终强制刷新，忽略时间缓存
          await loadMenuPermissions(newMenuName, true);
        } catch (error_) {
          console.error(`加载菜单 ${newMenuName} 权限失败:`, error_);
        }
      }
    },
    { immediate: true },
  );

  return {
    // 状态
    permissions,
    loading,
    workflowCode,
    stepCode,
    error: computed(() => error.value),
    currentMenuName,

    // 权限检查方法
    workflowGranted,
    workflowGrantedAny,
    workflowGrantedAll,

    // 权限管理方法
    loadMenuPermissions,
    refreshPermissions,
    clearPermissionCache,
  };
}
