import LogicFlow from '@logicflow/core';

export default function registerPolyline(lf: LogicFlow) {
  lf.register('polyline', ({ PolylineEdge, PolylineEdgeModel }) => {
    class ConnnectionModel extends PolylineEdgeModel {
      [x: string]: any;
      // eslint-disable-next-line @typescript-eslint/no-useless-constructor
      constructor(data: any, graphModel: any) {
        super(data, graphModel);
      }
      getEdgeAnimationStyle() {
        const style = super.getEdgeAnimationStyle();
        style.animationName = 'lf_animate_dash';
        return style;
      }
      setHovered(isHovered: any) {
        super.setHovered(isHovered);
        this.isAnimation = isHovered;
      }
    }
    return {
      view: PolylineEdge,
      model: ConnnectionModel,
    };
  });
}
