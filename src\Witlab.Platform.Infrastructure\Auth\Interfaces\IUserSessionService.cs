using Ardalis.Result;
using Witlab.Platform.Core.Platform;
using Witlab.Platform.Core.Platform.ValueObjects;

namespace Witlab.Platform.Infrastructure.Auth.Interfaces;

/// <summary>
/// 用户会话管理服务接口
/// </summary>
public interface IUserSessionService
{
  /// <summary>
  /// 创建用户会话
  /// </summary>
  /// <param name="userId">用户ID</param>
  /// <param name="userName">用户名</param>
  /// <param name="tokenId">JWT Token ID</param>
  /// <param name="tokenExpiresAt">Token过期时间</param>
  /// <param name="sessionInfo">会话信息</param>
  /// <param name="loginSource">登录来源</param>
  /// <returns>创建的会话</returns>
  Task<Result<UserSession>> CreateSessionAsync(
    Guid userId,
    string userName,
    string tokenId,
    DateTime tokenExpiresAt,
    SessionInfo sessionInfo,
    LoginSource loginSource = LoginSource.Web);

  /// <summary>
  /// 根据Token ID获取会话
  /// </summary>
  /// <param name="tokenId">JWT Token ID</param>
  /// <returns>会话信息</returns>
  Task<Result<UserSession?>> GetSessionByTokenIdAsync(string tokenId);

  /// <summary>
  /// 根据会话ID获取会话
  /// </summary>
  /// <param name="sessionId">会话ID</param>
  /// <returns>会话信息</returns>
  Task<Result<UserSession?>> GetSessionByIdAsync(Guid sessionId);

  /// <summary>
  /// 获取用户的所有活跃会话
  /// </summary>
  /// <param name="userId">用户ID</param>
  /// <returns>用户的活跃会话列表</returns>
  Task<Result<List<UserSession>>> GetActiveSessionsByUserIdAsync(Guid userId);

  /// <summary>
  /// 获取所有活跃会话
  /// </summary>
  /// <param name="pageNumber">页码</param>
  /// <param name="pageSize">页大小</param>
  /// <returns>活跃会话列表</returns>
  Task<Result<(List<UserSession> Sessions, int TotalCount)>> GetActiveSessionsAsync(int pageNumber = 1, int pageSize = 20);

  /// <summary>
  /// 更新会话活动时间
  /// </summary>
  /// <param name="tokenId">JWT Token ID</param>
  /// <returns>操作结果</returns>
  Task<Result> UpdateSessionActivityAsync(string tokenId);

  /// <summary>
  /// 终止会话（用户主动注销）
  /// </summary>
  /// <param name="tokenId">JWT Token ID</param>
  /// <returns>操作结果</returns>
  Task<Result> TerminateSessionAsync(string tokenId);

  /// <summary>
  /// 强制终止会话（管理员操作）
  /// </summary>
  /// <param name="sessionId">会话ID</param>
  /// <param name="reason">终止原因</param>
  /// <returns>操作结果</returns>
  Task<Result> ForceTerminateSessionAsync(Guid sessionId, string? reason = null);

  /// <summary>
  /// 强制终止用户的所有会话
  /// </summary>
  /// <param name="userId">用户ID</param>
  /// <param name="reason">终止原因</param>
  /// <returns>操作结果</returns>
  Task<Result> ForceTerminateAllUserSessionsAsync(Guid userId, string? reason = null);

  /// <summary>
  /// 清理过期会话
  /// </summary>
  /// <returns>清理的会话数量</returns>
  Task<Result<int>> CleanupExpiredSessionsAsync();

  /// <summary>
  /// 获取在线用户统计
  /// </summary>
  /// <returns>在线用户统计信息</returns>
  Task<Result<SessionStatistics>> GetSessionStatisticsAsync();

  /// <summary>
  /// 检查用户并发会话限制
  /// </summary>
  /// <param name="userId">用户ID</param>
  /// <param name="maxConcurrentSessions">最大并发会话数</param>
  /// <returns>是否超出限制</returns>
  Task<Result<bool>> CheckConcurrentSessionLimitAsync(Guid userId, int maxConcurrentSessions);
}

/// <summary>
/// 会话统计信息
/// </summary>
public class SessionStatistics
{
  /// <summary>
  /// 总在线用户数
  /// </summary>
  public int TotalOnlineUsers { get; set; }

  /// <summary>
  /// 总活跃会话数
  /// </summary>
  public int TotalActiveSessions { get; set; }

  /// <summary>
  /// Web会话数
  /// </summary>
  public int WebSessions { get; set; }

  /// <summary>
  /// 移动会话数
  /// </summary>
  public int MobileSessions { get; set; }

  /// <summary>
  /// 桌面会话数
  /// </summary>
  public int DesktopSessions { get; set; }

  /// <summary>
  /// API会话数
  /// </summary>
  public int ApiSessions { get; set; }

  /// <summary>
  /// 今日新增会话数
  /// </summary>
  public int TodayNewSessions { get; set; }

  /// <summary>
  /// 统计时间
  /// </summary>
  public DateTime StatisticsTime { get; set; } = DateTime.UtcNow;
}
