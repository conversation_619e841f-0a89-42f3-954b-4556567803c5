using System.IdentityModel.Tokens.Jwt;
using Microsoft.Extensions.Options;
using Witlab.Platform.Infrastructure.Auth;
using Witlab.Platform.Infrastructure.Auth.Interfaces;

namespace Witlab.Platform.Web.Middlewares;

/// <summary>
/// 会话跟踪中间件
/// </summary>
public class SessionTrackingMiddleware
{
  private readonly RequestDelegate _next;
  private readonly ILogger<SessionTrackingMiddleware> _logger;
  private readonly SessionSettings _sessionSettings;

  public SessionTrackingMiddleware(
    RequestDelegate next,
    ILogger<SessionTrackingMiddleware> logger,
    IOptions<SessionSettings> sessionSettings)
  {
    _next = next;
    _logger = logger;
    _sessionSettings = sessionSettings.Value;
  }

  public async Task InvokeAsync(HttpContext context)
  {
    // 如果未启用会话跟踪，直接跳过
    if (!_sessionSettings.EnableSessionTracking)
    {
      await _next(context);
      return;
    }

    // 检查是否为认证用户
    if (context.User.Identity?.IsAuthenticated == true)
    {
      await UpdateSessionActivityAsync(context);
    }

    await _next(context);
  }

  /// <summary>
  /// 更新会话活动时间
  /// </summary>
  private async Task UpdateSessionActivityAsync(HttpContext context)
  {
    try
    {
      // 获取Authorization头中的JWT token
      var authHeader = context.Request.Headers.Authorization.FirstOrDefault();
      if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
      {
        return;
      }

      var token = authHeader.Substring("Bearer ".Length).Trim();
      if (string.IsNullOrEmpty(token))
      {
        return;
      }

      // 检查是否需要更新活动时间（避免频繁更新）
      var lastUpdateKey = $"session_last_update_{token}";
      var lastUpdateTime = context.Session.GetString(lastUpdateKey);
      
      if (!string.IsNullOrEmpty(lastUpdateTime) && 
          DateTime.TryParse(lastUpdateTime, out var lastUpdate))
      {
        var timeSinceLastUpdate = DateTime.UtcNow - lastUpdate;
        if (timeSinceLastUpdate.TotalSeconds < _sessionSettings.ActivityUpdateIntervalSeconds)
        {
          // 距离上次更新时间太短，跳过本次更新
          return;
        }
      }

      // 从JWT token中提取JTI
      var jwtHandler = new JwtSecurityTokenHandler();
      var jwtToken = jwtHandler.ReadJwtToken(token);
      var jti = jwtToken.Claims.FirstOrDefault(c => c.Type == JwtRegisteredClaimNames.Jti)?.Value;

      if (string.IsNullOrEmpty(jti))
      {
        return;
      }

      // 更新会话活动时间
      var sessionService = context.RequestServices.GetRequiredService<IUserSessionService>();
      var result = await sessionService.UpdateSessionActivityAsync(jti);

      if (result.IsSuccess)
      {
        // 记录本次更新时间
        context.Session.SetString(lastUpdateKey, DateTime.UtcNow.ToString("O"));
        
        if (_sessionSettings.EnableDetailedLogging)
        {
          _logger.LogDebug("更新会话活动时间成功，JTI: {Jti}", jti);
        }
      }
      else if (_sessionSettings.EnableDetailedLogging)
      {
        _logger.LogWarning("更新会话活动时间失败，JTI: {Jti}，错误: {Errors}", 
          jti, string.Join(", ", result.Errors));
      }
    }
    catch (Exception ex)
    {
      // 会话跟踪失败不应该影响正常请求处理
      _logger.LogWarning(ex, "更新会话活动时间时发生错误");
    }
  }
}

/// <summary>
/// 会话跟踪中间件扩展方法
/// </summary>
public static class SessionTrackingMiddlewareExtensions
{
  /// <summary>
  /// 添加会话跟踪中间件
  /// </summary>
  /// <param name="builder">应用程序构建器</param>
  /// <returns>应用程序构建器</returns>
  public static IApplicationBuilder UseSessionTracking(this IApplicationBuilder builder)
  {
    return builder.UseMiddleware<SessionTrackingMiddleware>();
  }
}
