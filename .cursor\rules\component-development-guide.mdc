---
description: 
globs: *.vue
alwaysApply: false
---
# 组件开发指南

## 组件架构规范

### 组件分类
项目中的组件按功能分为以下几类：

1. **基础 UI 组件** - 位于 [packages/@core/ui-kit](mdc:packages/@core/ui-kit)
2. **业务组件** - 位于 [apps/web-antd/src/components](mdc:apps/web-antd/src/components)
3. **页面组件** - 位于 [apps/web-antd/src/views](mdc:apps/web-antd/src/views)
4. **布局组件** - 位于 [packages/effects/layouts](mdc:packages/effects/layouts)

### 组件命名规范
- 组件文件：使用 PascalCase，如 `UserManagement.vue`
- 组件目录：使用 kebab-case，如 `user-management/`
- 复合组件：使用命名空间，如 `VbenForm.vue`、`VbenTable.vue`

## Vue 3 组件开发规范

### 1. 组件结构模板
```vue
<script lang="ts" setup>
// 1. 类型导入
import type { PropType, ComponentPublicInstance } from 'vue';
import type { FormSchema } from '#/types';

// 2. 组件导入
import { VbenButton, VbenForm } from '@vben/common-ui';
import { useVbenForm } from '@vben/composables';

// 3. 工具函数导入
import { $t } from '#/locales';
import { message } from 'ant-design-vue';

// 4. Props 定义
interface Props {
  data?: any[];
  loading?: boolean;
  title?: string;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  loading: false,
  title: '',
});

// 5. Emits 定义
interface Emits {
  submit: [data: any];
  cancel: [];
}

const emit = defineEmits<Emits>();

// 6. 响应式状态
const visible = ref(false);
const formData = ref({});

// 7. 计算属性
const formattedTitle = computed(() => {
  return props.title || $t('common.defaultTitle');
});

// 8. 方法定义
function handleSubmit() {
  emit('submit', formData.value);
}

function handleCancel() {
  emit('cancel');
  visible.value = false;
}

// 9. 生命周期
onMounted(() => {
  console.log('组件已挂载');
});

// 10. 暴露给父组件的方法
defineExpose({
  open: () => { visible.value = true; },
  close: () => { visible.value = false; },
});
</script>

<template>
  <div class="component-container">
    <!-- 组件内容 -->
  </div>
</template>

<style lang="less" scoped>
.component-container {
  // 组件样式
}
</style>
```

### 2. 组合式函数 (Composables)
创建可复用的组合式函数位于 [apps/web-antd/src/hooks](mdc:apps/web-antd/src/hooks)：

```typescript
// hooks/useDialog.ts
export function useDialog() {
  const visible = ref(false);
  const loading = ref(false);
  
  const open = () => {
    visible.value = true;
  };
  
  const close = () => {
    visible.value = false;
  };
  
  const toggle = () => {
    visible.value = !visible.value;
  };
  
  return {
    visible: readonly(visible),
    loading: readonly(loading),
    open,
    close,
    toggle,
  };
}
```

## Vben UI 组件使用

### 1. 表单组件 (VbenForm)
```vue
<script lang="ts" setup>
import { useVbenForm } from '@vben/common-ui';

const schema: VbenFormSchema[] = [
  {
    component: 'Input',
    fieldName: 'username',
    label: $t('user.username'),
    rules: z.string().min(1, '请输入用户名'),
  },
  {
    component: 'Select',
    fieldName: 'role',
    label: $t('user.role'),
    componentProps: {
      options: [
        { label: '管理员', value: 'admin' },
        { label: '用户', value: 'user' },
      ],
    },
  },
];

const [Form, formApi] = useVbenForm({
  schema,
  showDefaultActions: true,
});

async function handleSubmit() {
  const { valid } = await formApi.validate();
  if (valid) {
    const values = await formApi.getValues();
    console.log('表单值:', values);
  }
}
</script>

<template>
  <Form @submit="handleSubmit" />
</template>
```

### 2. 表格组件 (VxeGrid)
```vue
<script lang="ts" setup>
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';

const columns: VxeGridPropTypes.Columns = [
  { field: 'name', title: '姓名', width: 120 },
  { field: 'email', title: '邮箱', width: 200 },
  { field: 'role', title: '角色', width: 100 },
  {
    title: '操作',
    width: 160,
    slots: { default: 'action' },
  },
];

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    columns,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const result = await getUserList({
            page: page.currentPage,
            pageSize: page.pageSize,
          });
          return result;
        },
      },
    },
  },
});

function handleEdit(row: any) {
  console.log('编辑:', row);
}

function handleDelete(row: any) {
  console.log('删除:', row);
}
</script>

<template>
  <Grid>
    <template #action="{ row }">
      <VbenButton @click="handleEdit(row)">编辑</VbenButton>
      <VbenButton danger @click="handleDelete(row)">删除</VbenButton>
    </template>
  </Grid>
</template>
```

## Logic Flow 工作流组件

### 工作流设计器
位于 [apps/web-antd/src/components/logic-flow](mdc:apps/web-antd/src/components/logic-flow)，这是一个基于 LogicFlow 的可视化工作流设计器。

#### 1. 基本使用
```vue
<script lang="ts" setup>
import { WorkflowDesigner } from '#/components/logic-flow';
import type { WorkflowData } from '#/components/logic-flow/types/workflow';

const workflowData = ref<WorkflowData>({
  nodes: [],
  edges: [],
});

function handleSave(data: WorkflowData) {
  console.log('保存工作流:', data);
  workflowData.value = data;
}

function handleExport() {
  // 导出工作流配置
}
</script>

<template>
  <WorkflowDesigner
    :data="workflowData"
    @save="handleSave"
    @export="handleExport"
  />
</template>
```

#### 2. 节点类型配置
节点配置位于 [apps/web-antd/src/components/logic-flow/config/nodes.ts](mdc:apps/web-antd/src/components/logic-flow/config/nodes.ts)：

```typescript
export const nodeConfig = {
  start: {
    type: 'start',
    label: '开始',
    icon: 'start',
    properties: {
      name: { type: 'string', required: true },
      description: { type: 'string' },
    },
  },
  userTask: {
    type: 'userTask',
    label: '用户任务',
    icon: 'user',
    properties: {
      assignee: { type: 'string', required: true },
      dueDate: { type: 'date' },
      formKey: { type: 'string' },
    },
  },
  condition: {
    type: 'condition',
    label: '条件判断',
    icon: 'condition',
    properties: {
      expression: { type: 'string', required: true },
      conditions: { type: 'array' },
    },
  },
  end: {
    type: 'end',
    label: '结束',
    icon: 'end',
    properties: {
      name: { type: 'string', required: true },
    },
  },
};
```

#### 3. 自定义节点开发
```typescript
// 注册自定义节点
import LogicFlow from '@logicflow/core';

class CustomNode extends RectNode {
  static extendKey = 'CustomNode';
  
  getNodeStyle() {
    const style = super.getNodeStyle();
    return {
      ...style,
      fill: '#1890ff',
      stroke: '#1890ff',
    };
  }
  
  getTextStyle() {
    const style = super.getTextStyle();
    return {
      ...style,
      color: '#ffffff',
    };
  }
}

// 注册到 LogicFlow
lf.register(CustomNode);
```

## 页面组件开发

### 1. 列表页面模板
```vue
<script lang="ts" setup>
import { useTable } from '#/hooks/useTable';
import { userApi } from '#/api/system/user';

// 表格配置
const { 
  tableData, 
  loading, 
  pagination, 
  refresh, 
  handleSearch 
} = useTable(userApi.getList);

// 搜索表单配置
const searchSchema: VbenFormSchema[] = [
  {
    component: 'Input',
    fieldName: 'keyword',
    label: '关键词',
    componentProps: {
      placeholder: '请输入用户名或邮箱',
    },
  },
  {
    component: 'Select',
    fieldName: 'role',
    label: '角色',
    componentProps: {
      options: roleOptions,
      placeholder: '请选择角色',
    },
  },
];

// 表格列配置
const columns = [
  { field: 'username', title: '用户名' },
  { field: 'email', title: '邮箱' },
  { field: 'role', title: '角色' },
  { field: 'createTime', title: '创建时间' },
  { title: '操作', slots: { default: 'action' } },
];

function handleCreate() {
  // 新建用户
}

function handleEdit(row: any) {
  // 编辑用户
}

function handleDelete(row: any) {
  // 删除用户
}
</script>

<template>
  <div class="page-container">
    <!-- 搜索栏 -->
    <VbenCard class="mb-4">
      <VbenForm 
        :schema="searchSchema" 
        @submit="handleSearch"
        layout="inline"
      />
    </VbenCard>

    <!-- 操作栏 -->
    <div class="mb-4">
      <VbenButton type="primary" @click="handleCreate">
        新建用户
      </VbenButton>
    </div>

    <!-- 数据表格 -->
    <VbenCard>
      <VxeGrid
        :data="tableData"
        :columns="columns"
        :loading="loading"
        :pager-config="pagination"
      >
        <template #action="{ row }">
          <VbenButton @click="handleEdit(row)">编辑</VbenButton>
          <VbenButton danger @click="handleDelete(row)">删除</VbenButton>
        </template>
      </VxeGrid>
    </VbenCard>
  </div>
</template>
```

### 2. 表单页面模板
```vue
<script lang="ts" setup>
import { useRoute, useRouter } from 'vue-router';
import { userApi } from '#/api/system/user';

const route = useRoute();
const router = useRouter();

const isEdit = computed(() => !!route.params.id);
const title = computed(() => isEdit.value ? '编辑用户' : '新建用户');

const schema: VbenFormSchema[] = [
  {
    component: 'Input',
    fieldName: 'username',
    label: '用户名',
    rules: z.string().min(1, '请输入用户名'),
  },
  {
    component: 'Input',
    fieldName: 'email',
    label: '邮箱',
    rules: z.string().email('请输入正确的邮箱格式'),
  },
  {
    component: 'Select',
    fieldName: 'role',
    label: '角色',
    componentProps: {
      options: roleOptions,
    },
  },
];

const [Form, formApi] = useVbenForm({
  schema,
  showDefaultActions: false,
});

async function handleSubmit() {
  const { valid } = await formApi.validate();
  if (!valid) return;

  const values = await formApi.getValues();
  
  try {
    if (isEdit.value) {
      await userApi.update(route.params.id as string, values);
      message.success('更新成功');
    } else {
      await userApi.create(values);
      message.success('创建成功');
    }
    router.back();
  } catch (error) {
    message.error(error.message || '操作失败');
  }
}

function handleCancel() {
  router.back();
}

// 编辑时加载数据
onMounted(async () => {
  if (isEdit.value) {
    const data = await userApi.getById(route.params.id as string);
    await formApi.setValues(data);
  }
});
</script>

<template>
  <div class="page-container">
    <VbenCard :title="title">
      <Form />
      
      <div class="mt-6 text-right">
        <VbenButton @click="handleCancel">取消</VbenButton>
        <VbenButton type="primary" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </VbenButton>
      </div>
    </VbenCard>
  </div>
</template>
```

## 样式规范

### 1. CSS 类命名规范
- 使用 BEM 命名规范
- 页面级样式：`.page-container`
- 组件级样式：`.component-name`
- 修饰符：`.component-name--modifier`

### 2. Tailwind CSS 使用
优先使用 Tailwind CSS 工具类：
```vue
<template>
  <div class="flex items-center justify-between p-4 bg-white rounded-lg shadow-sm">
    <h3 class="text-lg font-semibold text-gray-900">标题</h3>
    <VbenButton class="ml-4">操作</VbenButton>
  </div>
</template>
```

### 3. 响应式设计
```vue
<template>
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
    <VbenCard v-for="item in items" :key="item.id">
      <!-- 卡片内容 -->
    </VbenCard>
  </div>
</template>
```

## 组件测试

### 1. 单元测试
```typescript
// __tests__/components/UserForm.test.ts
import { mount } from '@vue/test-utils';
import { describe, it, expect } from 'vitest';
import UserForm from '../UserForm.vue';

describe('UserForm', () => {
  it('应该正确渲染表单', () => {
    const wrapper = mount(UserForm);
    expect(wrapper.find('.user-form').exists()).toBe(true);
  });

  it('应该验证必填字段', async () => {
    const wrapper = mount(UserForm);
    await wrapper.find('form').trigger('submit');
    expect(wrapper.find('.error-message').text()).toBe('请输入用户名');
  });
});
```

### 2. 组件文档
每个复杂组件都应该包含使用文档：
```typescript
/**
 * 用户管理表单组件
 * 
 * @example
 * ```vue
 * <UserForm
 *   :data="userData"
 *   @submit="handleSubmit"
 * />
 * ```
 */
export default defineComponent({
  name: 'UserForm',
  // ...
});
```

## 性能优化

### 1. 组件懒加载
```typescript
// 路由懒加载
const UserManagement = () => import('#/views/system/user/index.vue');

// 组件懒加载
const AsyncComponent = defineAsyncComponent(() => 
  import('#/components/HeavyComponent.vue')
);
```

### 2. 状态优化
```vue
<script lang="ts" setup>
// 使用 shallowRef 优化大对象
const largeData = shallowRef({});

// 使用 computed 缓存计算结果
const expensiveValue = computed(() => {
  return heavyCalculation(props.data);
});

// 使用 watchEffect 优化副作用
watchEffect(() => {
  if (props.visible) {
    loadData();
  }
});
</script>
```

### 3. 事件优化
```vue
<template>
  <!-- 使用事件修饰符 -->
  <button @click.stop="handleClick">点击</button>
  
  <!-- 防抖处理 -->
  <input @input="debounce(handleInput, 300)" />
  
  <!-- 节流处理 -->
  <div @scroll="throttle(handleScroll, 100)"></div>
</template>
```
