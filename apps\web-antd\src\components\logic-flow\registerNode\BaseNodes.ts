import type LogicFlow from '@logicflow/core';

/**
 * 注册基础矩形节点 - 现代化设计
 */
export function registerBaseRectNode(lf: LogicFlow) {
  lf.register('base-rect', ({ RectNode, RectNodeModel, h }: any) => {
    class BaseRectNode extends RectNode {
      getShape() {
        const { model } = this.props;
        const { x, y, width, height } = model;
        const { fill, stroke, strokeWidth } = model.getNodeStyle();

        return h('g', {}, [
          // 主体矩形
          h('rect', {
            x: x - width / 2,
            y: y - height / 2,
            width,
            height,
            fill,
            stroke,
            strokeWidth,
            rx: 8, // 圆角
            ry: 8,
          }),
          // 左侧装饰条
          h('rect', {
            x: x - width / 2,
            y: y - height / 2,
            width: 4,
            height,
            fill: model.properties?.accentColor || '#1890ff',
            rx: 2,
            ry: 2,
          }),
          // 节点文本
          h(
            'text',
            {
              x,
              y: y + 4,
              textAnchor: 'middle',
              fill: '#262626',
              fontSize: 14,
              fontWeight: 500,
            },
            model.properties?.name || '基础节点',
          ),
        ]);
      }
    }

    class BaseRectModel extends RectNodeModel {
      getNodeStyle() {
        const style = super.getNodeStyle();
        const { properties } = this;

        return {
          ...style,
          fill: properties?.backgroundColor || '#ffffff',
          stroke: properties?.borderColor || '#d9d9d9',
          strokeWidth: 1,
        };
      }

      initNodeData(data: any) {
        super.initNodeData(data);
        this.width = data.properties?.width || 120;
        this.height = data.properties?.height || 60;

        // 默认属性
        if (!this.properties) {
          this.properties = {};
        }
        this.properties = {
          name: '基础节点',
          backgroundColor: '#ffffff',
          borderColor: '#d9d9d9',
          accentColor: '#1890ff',
          ...this.properties,
        };
      }
    }

    return {
      view: BaseRectNode,
      model: BaseRectModel,
    };
  });
}

/**
 * 注册图标节点 - 支持 SVG 图标的现代化节点
 */
export function registerIconNode(lf: LogicFlow) {
  lf.register('icon-node', ({ RectNode, RectNodeModel, h }: any) => {
    class IconNode extends RectNode {
      // 获取预定义图标路径
      getPresetIconPaths() {
        return {
          // 星形
          star: 'M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z',
          // 心形
          heart:
            'M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z',
          // 用户
          user: 'M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2M12 11a4 4 0 1 0 0-8 4 4 0 0 0 0 8z',
          // 设置
          settings:
            'M12 15a3 3 0 1 0 0-6 3 3 0 0 0 0 6z M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z',
          // 文件
          file: 'M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9l-7-7z M13 2v7h7',
          // 邮件
          mail: 'M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z M22 6l-10 7L2 6',
          // 警告
          warning:
            'M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z M12 9v4 M12 17h.01',
          // 检查
          check: 'M20 6L9 17l-5-5',
          // 加号
          plus: 'M12 5v14 M5 12h14',
          // 搜索
          search: 'M21 21l-6-6m2-5a7 7 0 1 1-14 0 7 7 0 0 1 14 0z',
          // 首页
          home: 'M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z M9 22V12h6v10',
          // 下载
          download:
            'M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4 M7 10l5 5 5-5 M12 15V3',
          // 上传
          upload:
            'M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4 M17 8l-5-5-5 5 M12 3v12',
          // 数据库
          database:
            'M21 12c0 1.66-4 3-9 3s-9-1.34-9-3 M3 5c0 1.66 4 3 9 3s9-1.34 9-3 M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5',
          // 代码
          code: 'M16 18l6-6-6-6 M8 6l-6 6 6 6',
          // 锁
          lock: 'M19 11H5c-1.1 0-2 .9-2 2v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-7c0-1.1-.9-2-2-2z M7 11V7a5 5 0 0 1 10 0v4',
          // 钥匙
          key: 'M21 2l-2 2m-7.61 7.61a5.5 5.5 0 1 1-7.778 7.778 5.5 5.5 0 0 1 7.777-7.777zm0 0L15.5 7.5m0 0l3 3L22 7l-3-3m-3.5 3.5L19 4',
          // 时钟
          clock:
            'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z',
          // 地图
          map: 'M1 6v16l7-4 8 4 7-4V2l-7 4-8-4-7 4z M8 2v16 M16 6v16',
          // 图表
          chart: 'M18 20V10 M12 20V4 M6 20v-6',
        };
      }

      getShape() {
        const { model } = this.props;
        const { x, y, width, height } = model;
        const { fill, stroke, strokeWidth } = model.getNodeStyle();

        return h('g', {}, [
          // 主体矩形
          h('rect', {
            x: x - width / 2,
            y: y - height / 2,
            width,
            height,
            fill,
            stroke,
            strokeWidth,
            rx: 12,
            ry: 12,
          }),
          // 图标圆形背景
          h('circle', {
            cx: x - width / 2 + 24,
            cy: y - height / 2 + 24,
            r: 16,
            fill: model.properties?.iconBackground || '#f0f9ff',
            stroke: model.properties?.iconBorder || '#1890ff',
            strokeWidth: 2,
          }),
          // 渲染图标
          this.renderIcon(x - width / 2 + 24, y - height / 2 + 24, model, h),
          // 节点标题
          h(
            'text',
            {
              x: x - width / 2 + 52,
              y: y - height / 2 + 20,
              fill: '#262626',
              fontSize: 14,
              fontWeight: 600,
            },
            model.properties?.title || '图标节点',
          ),
          // 节点描述
          h(
            'text',
            {
              x: x - width / 2 + 52,
              y: y - height / 2 + 36,
              fill: '#8c8c8c',
              fontSize: 12,
            },
            model.properties?.description || '节点描述',
          ),
        ]);
      }

      // 渲染默认图标
      renderDefaultIcon(cx: number, cy: number, color: string, h: any) {
        return h('rect', {
          x: cx - 4,
          y: cy - 4,
          width: 8,
          height: 8,
          fill: color,
          rx: 1,
        });
      }

      // 渲染图标 - 统一入口，传递 h 函数
      renderIcon(cx: number, cy: number, model: any, h: any) {
        const iconType = model.properties?.iconType || 'preset';
        const iconColor = model.properties?.iconColor || '#1890ff';
        const iconSize = model.properties?.iconSize || 12;

        switch (iconType) {
          case 'path': {
            // SVG 路径图标
            return this.renderSvgPathIcon(
              cx,
              cy,
              model,
              iconColor,
              iconSize,
              h,
            );
          }
          case 'preset': {
            // 预定义图标
            return this.renderPresetIcon(cx, cy, model, iconColor, iconSize, h);
          }
          case 'text': {
            // 文本图标（Emoji 等）
            return this.renderTextIcon(cx, cy, model, iconColor, h);
          }
          default: {
            // 默认图标
            return this.renderDefaultIcon(cx, cy, iconColor, h);
          }
        }
      }

      // 渲染预定义图标
      renderPresetIcon(
        cx: number,
        cy: number,
        model: any,
        color: string,
        size: number,
        h: any,
      ) {
        const preset = model.properties?.iconPreset || 'star';
        const iconPaths = this.getPresetIconPaths();
        const iconPath =
          iconPaths[preset as keyof typeof iconPaths] || iconPaths.star;

        const scale = size / 24;
        const offset = size / 2;

        return h(
          'g',
          {
            transform: `translate(${cx - offset}, ${cy - offset}) scale(${scale})`,
          },
          [
            h('path', {
              d: iconPath,
              fill: color,
              stroke: 'none',
            }),
          ],
        );
      }

      // 渲染 SVG 路径图标
      renderSvgPathIcon(
        cx: number,
        cy: number,
        model: any,
        color: string,
        size: number,
        h: any,
      ) {
        const iconPath = model.properties?.iconPath;

        if (!iconPath) {
          return this.renderDefaultIcon(cx, cy, color, h);
        }

        const scale = size / 24; // 假设原始图标是 24x24
        const offset = size / 2;

        return h(
          'g',
          {
            transform: `translate(${cx - offset}, ${cy - offset}) scale(${scale})`,
          },
          [
            h('path', {
              d: iconPath,
              fill: color,
              stroke: 'none',
            }),
          ],
        );
      }

      // 渲染文本图标（Emoji 等）
      renderTextIcon(
        cx: number,
        cy: number,
        model: any,
        color: string,
        h: any,
      ) {
        const iconText = model.properties?.iconText || '🔥';
        const fontSize = model.properties?.iconSize || 16;

        return h(
          'text',
          {
            x: cx,
            y: cy + fontSize / 3,
            textAnchor: 'middle',
            fill: color,
            fontSize,
            fontWeight: 'bold',
          },
          iconText,
        );
      }
    }

    class IconModel extends RectNodeModel {
      getNodeStyle() {
        const style = super.getNodeStyle();
        return {
          ...style,
          fill: '#ffffff',
          stroke: '#e8e8e8',
          strokeWidth: 1,
        };
      }

      initNodeData(data: any) {
        super.initNodeData(data);
        this.width = 180;
        this.height = 48;

        this.properties = {
          title: '图标节点',
          description: '节点描述',
          // 图标配置
          iconType: 'preset', // 'path' | 'preset' | 'text'
          iconColor: '#1890ff',
          iconBackground: '#f0f9ff',
          iconBorder: '#1890ff',
          iconSize: 12,
          // 预定义图标
          iconPreset: 'star',
          // SVG 路径
          iconPath: '',
          // 文本图标（Emoji 等）
          iconText: '🔥',
          ...this.properties,
        };
      }
    }

    return {
      view: IconNode,
      model: IconModel,
    };
  });
}

/**
 * 注册卡片节点 - 现代化卡片设计
 */
export function registerCardNode(lf: LogicFlow) {
  lf.register('card-node', ({ RectNode, RectNodeModel, h }: any) => {
    class CardNode extends RectNode {
      getShape() {
        const { model } = this.props;
        const { x, y, width, height } = model;
        const name = model.properties?.name || '卡片节点';
        const description = model.properties?.description || '节点描述';

        return h('g', {}, [
          // 深度阴影效果
          h('rect', {
            x: x - width / 2 + 3,
            y: y - height / 2 + 3,
            width,
            height,
            fill: 'rgba(0,0,0,0.12)',
            rx: 16,
            ry: 16,
          }),
          // 中层柔和阴影
          h('rect', {
            x: x - width / 2 + 1,
            y: y - height / 2 + 1,
            width,
            height,
            fill: 'rgba(0,0,0,0.06)',
            rx: 15,
            ry: 15,
          }),
          // 主体卡片
          h('rect', {
            x: x - width / 2,
            y: y - height / 2,
            width,
            height,
            fill: '#ffffff',
            stroke: '#e8e8e8',
            strokeWidth: 1,
            rx: 14,
            ry: 14,
          }),
          // 顶部装饰渐变条
          h('defs', {}, [
            h(
              'linearGradient',
              {
                id: `card-gradient-${model.id}`,
                x1: '0%',
                y1: '0%',
                x2: '100%',
                y2: '0%',
              },
              [
                h('stop', {
                  offset: '0%',
                  stopColor: model.properties?.accentColor || '#52c41a',
                  stopOpacity: 1,
                }),
                h('stop', {
                  offset: '100%',
                  stopColor: model.properties?.accentColor || '#52c41a',
                  stopOpacity: 0.7,
                }),
              ],
            ),
          ]),
          h('rect', {
            x: x - width / 2,
            y: y - height / 2,
            width,
            height: 8,
            fill: `url(#card-gradient-${model.id})`,
            rx: 14,
            ry: 14,
          }),
          // 遮盖底部圆角
          h('rect', {
            x: x - width / 2,
            y: y - height / 2 + 4,
            width,
            height: 4,
            fill: `url(#card-gradient-${model.id})`,
          }),
          // 左侧图标装饰
          h('circle', {
            cx: x - width / 2 + 20,
            cy: y - height / 2 + 28,
            r: 8,
            fill: `rgba(${this.hexToRgb(
              model.properties?.accentColor || '#52c41a',
            )}, 0.1)`,
            stroke: model.properties?.accentColor || '#52c41a',
            strokeWidth: 1.5,
          }),
          h('circle', {
            cx: x - width / 2 + 20,
            cy: y - height / 2 + 28,
            r: 3,
            fill: model.properties?.accentColor || '#52c41a',
          }),
          // 节点名称（标题）
          h(
            'text',
            {
              x: x - width / 2 + 38,
              y: y - height / 2 + 26,
              fill: '#262626',
              fontSize: 14,
              fontWeight: 600,
              dominantBaseline: 'middle',
            },
            this.truncateText(name, 16),
          ),
          // 节点描述
          h(
            'text',
            {
              x: x - width / 2 + 38,
              y: y - height / 2 + 44,
              fill: '#8c8c8c',
              fontSize: 11,
              dominantBaseline: 'middle',
            },
            this.truncateText(description, 20),
          ),
          // 状态指示器组 - 右上角
          h('g', {}, [
            // 状态背景圆
            h('circle', {
              cx: x + width / 2 - 16,
              cy: y - height / 2 + 18,
              r: 6,
              fill: `rgba(${this.hexToRgb(
                model.properties?.statusColor || '#52c41a',
              )}, 0.15)`,
            }),
            // 状态点
            h('circle', {
              cx: x + width / 2 - 16,
              cy: y - height / 2 + 18,
              r: 3,
              fill: model.properties?.statusColor || '#52c41a',
            }),
          ]),
          // 底部装饰线
          h('rect', {
            x: x - width / 2 + 12,
            y: y + height / 2 - 12,
            width: width - 24,
            height: 1,
            fill: '#f0f0f0',
          }),
          // 底部标签
          h(
            'text',
            {
              x: x - width / 2 + 14,
              y: y + height / 2 - 4,
              fill: '#bfbfbf',
              fontSize: 9,
              fontWeight: 500,
            },
            model.properties?.category || 'CARD',
          ),
          // 右下角小装饰
          h('circle', {
            cx: x + width / 2 - 8,
            cy: y + height / 2 - 8,
            r: 2,
            fill: '#f0f0f0',
          }),
        ]);
      }

      // 十六进制颜色转RGB工具方法
      hexToRgb(hex: string | undefined): string {
        if (!hex) return '82, 196, 26'; // 默认绿色
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        if (result) {
          const r = Number.parseInt(result[1], 16);
          const g = Number.parseInt(result[2], 16);
          const b = Number.parseInt(result[3], 16);
          return `${r}, ${g}, ${b}`;
        }
        return '82, 196, 26'; // 默认绿色
      }

      // 文本截断工具方法
      truncateText(text: string | undefined, maxLength: number): string {
        if (!text) return '';
        if (text.length <= maxLength) return text;
        return `${text.slice(0, Math.max(0, maxLength))}...`;
      }
    }

    class CardModel extends RectNodeModel {
      initNodeData(data: any) {
        super.initNodeData(data);
        this.width = 200;
        this.height = 100;

        this.properties = {
          name: '卡片节点',
          description: '这是一个美观的卡片节点',
          accentColor: '#52c41a',
          statusColor: '#52c41a',
          category: 'CARD',
          ...this.properties,
        };
      }
    }

    return {
      view: CardNode,
      model: CardModel,
    };
  });
}

/**
 * 注册状态节点 - 可显示不同状态的节点
 */
export function registerStatusNode(lf: LogicFlow) {
  lf.register('status-node', ({ CircleNode, CircleNodeModel, h }: any) => {
    class StatusNode extends CircleNode {
      getShape() {
        const { model } = this.props;
        const { x, y, r } = model;
        const status = model.properties?.status || 'default';

        // 状态颜色映射
        const statusColors: Record<string, string> = {
          success: '#52c41a',
          warning: '#faad14',
          error: '#ff4d4f',
          processing: '#1890ff',
          default: '#d9d9d9',
        };

        const color = statusColors[status] || statusColors.default;

        return h('g', {}, [
          // 外圆环
          h('circle', {
            cx: x,
            cy: y,
            r: r + 4,
            fill: color,
            opacity: 0.2,
          }),
          // 主圆
          h('circle', {
            cx: x,
            cy: y,
            r,
            fill: '#ffffff',
            stroke: color,
            strokeWidth: 3,
          }),
          // 状态图标/文字
          h(
            'text',
            {
              x,
              y: y + 4,
              textAnchor: 'middle',
              fill: color,
              fontSize: 12,
              fontWeight: 'bold',
            },
            this.getStatusIcon(status),
          ),
          // 状态文本
          h(
            'text',
            {
              x,
              y: y + r + 20,
              textAnchor: 'middle',
              fill: '#262626',
              fontSize: 12,
            },
            model.properties?.label || '状态节点',
          ),
        ]);
      }

      getStatusIcon(status: string) {
        const icons: Record<string, string> = {
          success: '✓',
          warning: '⚠',
          error: '✗',
          processing: '⟳',
          default: '●',
        };
        return icons[status] || icons.default;
      }
    }

    class StatusModel extends CircleNodeModel {
      initNodeData(data: any) {
        super.initNodeData(data);
        this.r = 24;

        this.properties = {
          status: 'default', // success, warning, error, processing, default
          label: '状态节点',
          ...this.properties,
        };
      }
    }

    return {
      view: StatusNode,
      model: StatusModel,
    };
  });
}

/**
 * 注册流程节点 - 专门用于流程步骤的节点
 */
export function registerProcessNode(lf: LogicFlow) {
  lf.register('process-node', ({ PolygonNode, PolygonNodeModel, h }: any) => {
    class ProcessNode extends PolygonNode {
      getShape() {
        const { model } = this.props;
        const { x, y, points } = model;
        const pointStr = points
          .map((point) => {
            return `${point[0] + x - model.width / 2}, ${point[1] + y - model.height / 2}`;
          })
          .join(' ');

        // 计算边界框用于UI元素定位
        const width = 140;
        const height = 60;

        return h('g', {}, [
          // 阴影
          h('polygon', {
            points: pointStr,
            fill: 'rgba(0,0,0,0.1)',
            transform: `translate(2, 2)`,
          }),
          // 主体
          h('polygon', {
            points: pointStr,
            fill: model.properties?.backgroundColor || '#e6f7ff',
            stroke: model.properties?.borderColor || '#1890ff',
            strokeWidth: 2,
          }),
          // 序号圆圈
          h('circle', {
            cx: x - width / 2 + 20,
            cy: y - height / 2 + 15,
            r: 8,
            fill: model.properties?.numberBackground || '#1890ff',
          }),
          h(
            'text',
            {
              x: x - width / 2 + 20,
              y: y - height / 2 + 19,
              textAnchor: 'middle',
              fill: '#ffffff',
              fontSize: 10,
              fontWeight: 'bold',
            },
            model.properties?.number || '1',
          ),
          // 标题
          h(
            'text',
            {
              x,
              y: y - 8,
              textAnchor: 'middle',
              fill: '#262626',
              fontSize: 14,
              fontWeight: 600,
            },
            model.properties?.title || '流程步骤',
          ),
          // 描述
          h(
            'text',
            {
              x,
              y: y + 8,
              textAnchor: 'middle',
              fill: '#8c8c8c',
              fontSize: 11,
            },
            model.properties?.description || '步骤描述',
          ),
        ]);
      }
    }

    class ProcessModel extends PolygonNodeModel {
      initNodeData(data: any) {
        super.initNodeData(data);

        // 为多边形节点设置点数组来定义形状和大小
        const width = 140;
        const height = 60;

        // 设置六边形的点（相对于中心点的偏移）
        this.points = [
          [-width / 2 + 10, -height / 2], // 左上
          [width / 2 - 10, -height / 2], // 右上
          [width / 2, 0], // 右中
          [width / 2 - 10, height / 2], // 右下
          [-width / 2 + 10, height / 2], // 左下
          [-width / 2, 0], // 左中
        ];

        this.properties = {
          title: '流程步骤',
          description: '步骤描述',
          number: '1',
          backgroundColor: '#e6f7ff',
          borderColor: '#1890ff',
          numberBackground: '#1890ff',
          ...this.properties,
        };
      }
    }

    return {
      view: ProcessNode,
      model: ProcessModel,
    };
  });
}

/**
 * 注册分组节点 - 可以包含其他节点的容器节点
 */
export function registerGroupNode(lf: LogicFlow) {
  lf.register('group-node', ({ RectNode, RectNodeModel, h }: any) => {
    class GroupNode extends RectNode {
      getShape() {
        const { model } = this.props;
        const { x, y, width, height } = model;

        return h('g', {}, [
          // 主体矩形 - 虚线边框
          h('rect', {
            x: x - width / 2,
            y: y - height / 2,
            width,
            height,
            fill:
              model.properties?.backgroundColor || 'rgba(24, 144, 255, 0.05)',
            stroke: model.properties?.borderColor || '#1890ff',
            strokeWidth: 2,
            strokeDasharray: '8,4',
            rx: 8,
            ry: 8,
          }),
          // 标题背景
          h('rect', {
            x: x - width / 2 + 10,
            y: y - height / 2 + 10,
            width: (model.properties?.title?.length || 4) * 8 + 16,
            height: 24,
            fill: '#ffffff',
            stroke: model.properties?.borderColor || '#1890ff',
            strokeWidth: 1,
            rx: 4,
          }),
          // 标题文字
          h(
            'text',
            {
              x: x - width / 2 + 18,
              y: y - height / 2 + 26,
              fill: model.properties?.titleColor || '#1890ff',
              fontSize: 12,
              fontWeight: 600,
            },
            model.properties?.title || '分组',
          ),
        ]);
      }
    }

    class GroupModel extends RectNodeModel {
      initNodeData(data: any) {
        super.initNodeData(data);
        this.width = data.properties?.width || 200;
        this.height = data.properties?.height || 150;

        this.properties = {
          title: '分组',
          backgroundColor: 'rgba(24, 144, 255, 0.05)',
          borderColor: '#1890ff',
          titleColor: '#1890ff',
          ...this.properties,
        };
      }
    }

    return {
      view: GroupNode,
      model: GroupModel,
    };
  });
}

/**
 * 注册所有基础节点
 */
export function registerAllBaseNodes(lf: LogicFlow) {
  registerBaseRectNode(lf);
  registerIconNode(lf);
  registerCardNode(lf);
  registerStatusNode(lf);
  registerProcessNode(lf);
  registerGroupNode(lf);
}
